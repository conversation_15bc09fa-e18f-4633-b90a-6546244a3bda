<?php

namespace Database\Seeders\v0_3_0;

use App\System\Apps\Data\AppType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InstancesTypesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('instances')->update(['app_type' => AppType::DMS->value]);
        DB::table('instances')->where('name', '=', 'pwa')->update(['app_type' => AppType::PWA->value]);
    }
}
