<?php

namespace Database\Seeders\v0_3_2;

use App\Models\Infrastructure\Group;
use App\System\Apps\Data\AppType;
use Exception;
use Illuminate\Database\Seeder;

class PwaSshConnectionsSeeder extends Seeder
{
    public function run(): void
    {
        Group::withTrashed()->with('instances')->each(function ($group) {
            $dms = $group->instances->firstWhere('app_type', '=', AppType::DMS);
            $pwa = $group->instances->firstWhere('app_type', '=', AppType::PWA);

            if ($dms && $pwa) {
                $dmsSshConnection = $dms->sshConnections()->first();
                if (! $dmsSshConnection) {
                    report(new Exception(sprintf('Existing DMS ID %d does not have SSH connection!', $dms->id)));
                }

                $pwa->sshConnections()->attach($dmsSshConnection);
            }
        });
    }
}
