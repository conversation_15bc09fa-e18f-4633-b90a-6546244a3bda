<?php

namespace Database\Factories\Infrastructure\Connections;

use Illuminate\Database\Eloquent\Factories\Factory;

class SshConnectionFactory extends Factory
{
    public function definition(): array
    {
        $userName = fake()->userName();

        return [
            'host' => fake()->ipv4(),
            'user' => $userName,
            'private_key' => "/home/<USER>/.ssh/{$userName}",
            'timeout' => fake()->optional()->randomElement([10, 20, 30, 60]),
            'remove_bash' => fake()->boolean(10),
        ];
    }
}
