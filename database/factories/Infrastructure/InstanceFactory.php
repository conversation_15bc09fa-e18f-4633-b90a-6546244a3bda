<?php

namespace Database\Factories\Infrastructure;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Data\DmsInstanceData;
use App\System\Infrastructure\Instances\Data\Hosting;
use App\System\Infrastructure\Instances\Data\OauthClient;
use App\System\Infrastructure\Instances\Data\PusherData;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstanceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'group_id' => Group::factory(),
            'name' => fake()->unique()->word() . '_instance',
            'app_type' => fake()->randomElement(AppType::cases()),
        ];
    }

    public function dms(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'app_type' => AppType::DMS,
            'data' => new DmsInstanceData(
                $attributes['name'],
                fake()->word(),
                fake()->word(),
                fake()->url(),
                Hosting::HETZNER_CLOUD,
                fake()->ipv4(),
                fake()->uuid(),
                fake()->word(),
                [
                    'pwa' => new OauthClient(
                        fake()->md5(),
                        fake()->md5(),
                    ),
                ],
                new PusherData(
                    fake()->md5(),
                    fake()->md5(),
                    fake()->md5(),
                ),
            ),
        ])->hasAttached(SshConnection::factory()->count(1));
    }
}
