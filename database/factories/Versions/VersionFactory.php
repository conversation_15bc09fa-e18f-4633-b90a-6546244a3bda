<?php

namespace Database\Factories\Versions;

use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class VersionFactory extends Factory
{
    public static $usedOrders = [];

    public function definition(): array
    {
        $version = fake()->unique()->semver();

        return [
            'app_type' => fake()->randomElement(AppType::cases()),
            'order' => fn () => $this->getUnusedOrder(),
            'tag' => 'tag-' . Str::slug($version) . '-' . fake()->unique()->md5(),
            'name' => $version,
            'description' => fake()->sentences(asText: true),
            'docker_compose_yaml' => fake()->sentences(asText: true),
            'docker_compose_env' => fake()->sentences(asText: true),
        ];
    }

    public function configure(): static
    {
        return $this->afterMaking(function (Version $version) {
            self::$usedOrders[] = $version->order;
        });
    }

    public function dms(): Factory
    {
        return $this->state(fn () => [
            'app_type' => AppType::DMS,
        ]);
    }

    private function getUnusedOrder(): int
    {
        do {
            $order = fake()->unique()->randomNumber();
        } while (in_array($order, self::$usedOrders));

        return $order;
    }
}
