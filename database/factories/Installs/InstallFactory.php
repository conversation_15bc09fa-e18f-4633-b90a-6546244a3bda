<?php

namespace Database\Factories\Installs;

use App\Models\Installs\Install;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstallFactory extends Factory
{
    public function definition(): array
    {
        return [
            'type' => fake()->randomElement(InstallType::cases()),
            'payload' => [],
        ];
    }

    public function configure(): static
    {
        return $this->afterCreating(function (Install $install) {
            $install->setStatus(InstallStatus::PENDING);
        });
    }

    public function running(): Factory
    {
        return $this->state(fn () => [])->afterCreating(function (Install $install) {
            $install->setStatus(InstallStatus::RUNNING);
        });
    }
}
