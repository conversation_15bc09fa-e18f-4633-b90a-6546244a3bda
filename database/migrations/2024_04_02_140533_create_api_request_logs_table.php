<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('api_request_logs', function (Blueprint $table) {
            $table->id();

            $table->nullableMorphs('model');

            $table->string('request_url')->nullable();
            $table->string('request_method')->nullable();
            $table->json('request_headers')->nullable();
            $table->json('request_body')->nullable();
            $table->ipAddress('request_ip')->nullable();
            $table->unsignedSmallInteger('response_http_code')->nullable();
            $table->json('response_body')->nullable();
            $table->json('response_headers')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('api_request_logs');
    }
};
