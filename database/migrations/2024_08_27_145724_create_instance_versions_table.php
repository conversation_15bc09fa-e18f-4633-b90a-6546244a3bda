<?php

use App\Models\Infrastructure\Instance;
use App\Models\Versions\Version;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('instance_version', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Instance::class)->constrained();
            $table->foreignIdFor(Version::class)->constrained();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instance_version');
    }
};
