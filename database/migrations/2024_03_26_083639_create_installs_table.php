<?php

use App\Models\Infrastructure\Group;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('installs', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Group::class)->nullable();
            $table->string('type');
            $table->json('payload');

            $table->timestamp('ended_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('installs');
    }
};
