<?php

use App\System\Apps\Data\AppType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('instances', function (Blueprint $table) {
            $table->string('app_type')->index()->default(AppType::DMS->value)->after('id');
        });
    }

    public function down(): void
    {
        Schema::table('instances', function (Blueprint $table) {
            $table->dropColumn('app_type');
        });
    }
};
