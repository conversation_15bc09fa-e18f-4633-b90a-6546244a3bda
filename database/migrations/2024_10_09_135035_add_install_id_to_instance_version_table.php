<?php

use App\Models\Installs\Install;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('instance_version', function (Blueprint $table) {
            $table->foreignIdFor(Install::class)->after('version_id')->nullable()->constrained();
        });
    }

    public function down(): void
    {
        Schema::table('instance_version', function (Blueprint $table) {
            $table->dropForeignIdFor(Install::class);
        });
    }
};
