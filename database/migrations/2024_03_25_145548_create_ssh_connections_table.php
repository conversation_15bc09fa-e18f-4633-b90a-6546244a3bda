<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('ssh_connections', function (Blueprint $table) {
            $table->id();

            $table->string('host');
            $table->string('user');
            $table->string('private_key');
            $table->unsignedInteger('timeout')->nullable();
            $table->boolean('remove_bash')->default(false);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ssh_connections');
    }
};
