<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('resource_sentry_projects', function (Blueprint $table) {
            $table->id();

            $table->string('external_id')->index();
            $table->string('slug');
            $table->string('name');
            $table->json('client_key')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('resource_sentry_projects');
    }
};
