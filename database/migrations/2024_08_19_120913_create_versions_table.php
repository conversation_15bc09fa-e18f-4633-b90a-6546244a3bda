<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('versions', function (Blueprint $table) {
            $table->id();

            $table->string('app_type')->index();
            $table->unsignedInteger('order')->index();
            $table->string('tag')->unique();
            $table->string('name')->unique();
            $table->text('description')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('versions');
    }
};
