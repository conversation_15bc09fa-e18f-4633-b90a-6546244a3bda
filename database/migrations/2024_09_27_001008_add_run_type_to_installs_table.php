<?php

use App\System\Installs\Data\RunType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('installs', function (Blueprint $table) {
            $table->string('run_type')->index()->default(RunType::ROLLBACK_ON_FIRST_ERROR)->after('type');
        });
    }

    public function down(): void
    {
        Schema::table('installs', function (Blueprint $table) {
            $table->dropColumn('run_type');
        });
    }
};
