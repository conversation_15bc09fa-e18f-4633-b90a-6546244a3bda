<?php

use App\Models\Infrastructure\Instance;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('instance_connection', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Instance::class)->constrained();
            $table->morphs('connection');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instance_connection');
    }
};
