<?php

use App\Models\Installs\Install;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('stages', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Install::class)->constrained();
            $table->string('name');
            $table->string('implementation');
            $table->string('implementation_method');
            $table->json('scope')->nullable();
            $table->json('prior_context')->nullable();
            $table->json('resulting_context')->nullable();
            $table->unsignedInteger('order')->default(0);

            $table->timestamp('ended_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stages');
    }
};
