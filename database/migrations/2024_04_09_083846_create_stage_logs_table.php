<?php

use App\Models\Installs\Stage;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('stage_logs', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Stage::class)->constrained();
            $table->string('type')->index();
            $table->longText('content')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stage_logs');
    }
};
