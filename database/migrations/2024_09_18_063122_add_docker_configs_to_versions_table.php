<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('versions', function (Blueprint $table) {
            $table->longText('docker_compose_yaml')->nullable()->after('description');
            $table->longText('docker_compose_env')->nullable()->after('docker_compose_yaml');
        });
    }

    public function down(): void
    {
        Schema::table('versions', function (Blueprint $table) {
            $table->dropColumn(['docker_compose_env', 'docker_compose_yaml']);
        });
    }
};
