<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('command_logs', function (Blueprint $table) {
            $table->id();

            $table->nullableMorphs('model');
            $table->string('type')->index();
            $table->json('payload')->nullable();
            $table->json('result')->nullable();
            $table->json('error')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('command_logs');
    }
};
