<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('installs', function (Blueprint $table) {
            $table->json('resulting_context')->nullable()->after('payload');
            $table->json('result')->nullable()->after('resulting_context');
        });
    }

    public function down(): void
    {
        Schema::table('installs', function (Blueprint $table) {
            $table->dropColumn('resulting_context', 'result');
        });
    }
};
