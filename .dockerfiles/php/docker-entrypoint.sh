#!/bin/bash

set -ex

cd /var/www/html/deployer

env | sed -E 's/([^=]+)=(.*)/\1="\2"/' > .env

echo "${SSH_KEY_BASE64}" | base64 -d > .ssh_key
chmod 600 .ssh_key


rsync --chown=www-data:www-data --force --stats -a /.init/public/ /var/www/html/deployer/public/
rsync --chown=www-data:www-data --force --stats -a /.init/storage/ /var/www/html/deployer/storage/

composer.phar install --no-dev --no-plugins --optimize-autoloader

npm run build

php artisan migrate --force
php artisan db:seed --force
#php artisan passport:keys
php artisan l5-swagger:generate --all

chown -R www-data:www-data ./

exec "$@"

