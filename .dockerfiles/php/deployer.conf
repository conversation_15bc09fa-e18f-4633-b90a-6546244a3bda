[program:fpm]
command         = docker-php-entrypoint php-fpm -R
directory       = /var/www/html/deployer
process_name    = fpm%(process_num)s
numprocs        = 1
umask           = 022
autostart       = true
autorestart     = true
startsecs       = 3
user            = www-data
stdout_logfile  = /var/log/fpm.log
stdout_logfile_maxbytes = 8MB
stdout_logfile_backups = 3
redirect_stderr = true

[program:artisan_horizon]
command         = /usr/local/bin/php artisan horizon
directory       = /var/www/html/deployer
process_name    = artisan_horizon%(process_num)s
numprocs        = 1
umask           = 022
autostart       = true
autorestart     = true
startsecs       = 3
user            = www-data
stdout_logfile  = /var/log/artisan_horizon.log
stdout_logfile_maxbytes = 8MB
stdout_logfile_backups = 3
redirect_stderr = true

[program:cron]
command         = /usr/sbin/cron -f -L 15
directory       = /var/www/html/deployer
process_name    = cron%(process_num)s
numprocs        = 1
umask           = 022
autostart       = true
autorestart     = true
startsecs       = 3
#user            = www-data
stdout_logfile  = /var/log/cron.log
stdout_logfile_maxbytes = 8MB
stdout_logfile_backups = 3
redirect_stderr = true
