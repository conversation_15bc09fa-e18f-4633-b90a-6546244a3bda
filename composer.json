{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "darkaonline/l5-swagger": "^8.6", "inertiajs/inertia-laravel": "^1.3", "laravel/framework": "^11.0", "laravel/horizon": "^5.24", "laravel/passport": "^12.0", "laravel/tinker": "^2.9", "predis/predis": "^2.2", "sentry/sentry-laravel": "^4.5", "spatie/laravel-model-status": "^1.15", "spatie/ssh": "^1.10"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/breeze": "^2.0", "laravel/pint": "^1.14", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"files": ["app/Support/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}