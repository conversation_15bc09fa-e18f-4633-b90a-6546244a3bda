<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'hetzner' => [
        'cloud' => [
            'api_token' => env('HETZNER_CLOUD_API_TOKEN'),
            'url' => env('HETZNER_CLOUD_API_URL'),
            'firewall_names' => explode(',', env('HETZNER_CLOUD_FIREWALL_NAMES') ?: ''),
            'network_names' => explode(',', env('HETZNER_CLOUD_NETWORK_NAMES') ?: ''),
            'ssh_key_names' => explode(',', env('HETZNER_CLOUD_SSH_KEY_NAMES') ?: ''),
            'ssh_key_file' => env('HETZNER_CLOUD_SSH_KEY_FILE'),
            'server_name_template' => env('HETZNER_CLOUD_SERVER_NAME_TEMPLATE', '%s'),
            'server_type' => [
                'dms' => env('HETZNER_CLOUD_SERVER_TYPE_DMS'),
            ],
        ],
        'dns' => [
            'api_token' => env('HETZNER_DNS_API_TOKEN'),
            'url' => env('HETZNER_DNS_API_URL'),
        ],
    ],
];
