<?php

return [

    /*
     * The class name of the status model that holds all statuses.
     *
     * The model must be or extend `<PERSON><PERSON>\ModelStatus\Status`.
     */
    'status_model' => <PERSON><PERSON>\ModelStatus\Status::class,

    /*
     * The name of the attribute to access the latest status.
     *
     * You can change this value if you have need a custom status attribute.
     */
    'status_attribute' => 'status',

    /*
     * The name of the column which holds the ID of the model related to the statuses.
     *
     * You can change this value if you have set a different name in the migration for the statuses table.
     */
    'model_primary_key_attribute' => 'model_id',
];
