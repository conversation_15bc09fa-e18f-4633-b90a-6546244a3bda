<?php

return [
    'environment' => env('DEPLOY_ENVIRONMENT'),

    'zbx' => [
        'metadata' => env('DEPLOY_ZBX_METADATA'),
    ],

    'dms' => [
        'zone_name' => env('DEPLOY_DMS_ZONE_NAME'),
        'ssh_user' => 'root',
        'compose_project_name' => 'dms',
        'registry' => env('DEPLOY_DMS_REGISTRY'),
        'registry_user' => env('DEPLOY_DMS_REGISTRY_USER'),
        'registry_password' => env('DEPLOY_DMS_REGISTRY_PASSWORD'),
        'tag' => env('DEPLOY_DMS_TAG'),

        'elasticsearch' => [
            'host' => env('DEPLOY_DMS_ELASTICSEARCH_HOST'),
            'enable_auth' => env('DEPLOY_DMS_ELASTICSEARCH_ENABLE_AUTH', false),
            'user' => env('DEPLOY_DMS_ELASTICSEARCH_USER'),
            'generate_password' => env('DEPLOY_DMS_ELASTICSEARCH_GENERATE_PASSWORD', false),
            'password' => env('DEPLOY_DMS_ELASTICSEARCH_PASSWORD'),
        ],

        'pdf_base_url' => env('DEPLOY_DMS_PDF_BASE_URL'),

        'sentry' => [
            'dsn' => env('DEPLOY_DMS_SENTRY_DSN'),
            'release' => env('DEPLOY_DMS_SENTRY_RELEASE'),
            'environment' => env('DEPLOY_DMS_SENTRY_ENVIRONMENT'),
        ],

        'google_maps' => [
            'api_key' => env('DEPLOY_DMS_GOOGLE_MAPS_API_KEY'),
        ],

        'aws' => [
            'url' => env('DEPLOY_DMS_AWS_URL'),
            'bucket' => env('DEPLOY_DMS_AWS_BUCKET'),
            'access_key_id' => env('DEPLOY_DMS_AWS_ACCESS_KEY_ID'),
            'access_secret_key' => env('DEPLOY_DMS_AWS_ACCESS_SECRET_KEY'),
        ],

        'mail' => [
            'mailer' => env('DEPLOY_DMS_MAIL_MAILER'),
            'host' => env('DEPLOY_DMS_MAIL_HOST'),
            'port' => env('DEPLOY_DMS_MAIL_PORT'),
            'username' => env('DEPLOY_DMS_MAIL_USERNAME'),
            'password' => env('DEPLOY_DMS_MAIL_PASSWORD'),
            'encryption' => env('DEPLOY_DMS_MAIL_ENCRYPTION'),
            'from_address' => env('DEPLOY_DMS_MAIL_FROM_ADDRESS'),
        ],

        'marketplaces_encryption_key' => env('DEPLOY_DMS_MARKETPLACES_ENCRYPTION_KEY'),
    ],

    'cdn' => [
        'url' => env('DEPLOY_CDN_URL'),
        'minio_host' => env('DEPLOY_CDN_MINIO_HOST'),
        'basic_auth' => [
            'user' => env('DEPLOY_CDN_BASIC_AUTH_USER'),
            'password' => env('DEPLOY_CDN_BASIC_AUTH_PASSWORD'),
        ],
        'cicd_access_key' => env('DEPLOY_CDN_CICD_ACCESS_KEY'),
        'cicd_secret_key' => env('DEPLOY_CDN_CICD_SECRET_KEY'),
    ],

    'sentry' => [
        'url' => env('DEPLOY_SENTRY_URL'),
        'dms_project_name_template' => env('DEPLOY_SENTRY_DMS_PROJECT_NAME_TEMPLATE'),
        'pwa_project_name_template' => env('DEPLOY_SENTRY_PWA_PROJECT_NAME_TEMPLATE'),
        'organization_slug' => env('DEPLOY_SENTRY_ORGANIZATION_SLUG'),
        'team_slug' => env('DEPLOY_SENTRY_TEAM_SLUG'),
        'auth_token' => env('DEPLOY_SENTRY_AUTH_TOKEN'),
    ],

    'checker' => [
        'ssh_user' => env('DEPLOY_CHECKER_SSH_USER'),
        'host' => env('DEPLOY_CHECKER_HOST'),
        'url' => env('DEPLOY_CHECKER_URL'),
    ],

    'ai_service' => [
        'ssh_user' => env('DEPLOY_AI_SERVICE_SSH_USER'),
        'host' => env('DEPLOY_AI_SERVICE_HOST'),
        'url' => env('DEPLOY_AI_SERVICE_URL'),
    ],

	'scrm' => [
		'ssh_user' => env('DEPLOY_SCRM_SSH_USER'),
		'host' => env('DEPLOY_SCRM_HOST'),
		'url' => env('DEPLOY_SCRM_URL'),
    ],

    'envelope' => [
        'ssh_user' => env('DEPLOY_ENVELOPE_SSH_USER'),
        'host' => env('DEPLOY_ENVELOPE_HOST'),
        'url' => env('DEPLOY_ENVELOPE_URL'),
    ],

    'pwa' => [
        'zone_name' => env('DEPLOY_PWA_ZONE_NAME'),
        'url' => env('DEPLOY_PWA_URL'),
        'client_id' => env('DEPLOY_PWA_API_CLIENT_ID'),
        'client_secret' => env('DEPLOY_PWA_API_CLIENT_SECRET'),
        'admin_url' => env('DEPLOY_PWA_ADMIN_URL'),
        'admin_client_id' => env('DEPLOY_PWA_ADMIN_CLIENT_ID'),
        'admin_secret' => env('DEPLOY_PWA_ADMIN_SECRET'),

        'vapid_public_key' => env('DEPLOY_PWA_VAPID_PUBLIC_KEY'),
        'vapid_private_key' => env('DEPLOY_PWA_VAPID_PRIVATE_KEY'),

        'compose_project_name' => 'pwa',
        'registry' => env('DEPLOY_PWA_REGISTRY'),
        'registry_user' => env('DEPLOY_PWA_REGISTRY_USER'),
        'registry_password' => env('DEPLOY_PWA_REGISTRY_PASSWORD'),
        'tag' => env('DEPLOY_PWA_TAG'),

        'sentry' => [
            'dsn' => env('DEPLOY_PWA_SENTRY_DSN'),
            'release' => env('DEPLOY_PWA_SENTRY_RELEASE'),
            'environment' => env('DEPLOY_PWA_SENTRY_ENVIRONMENT'),
        ],

        'mail' => [
            'mailer' => env('DEPLOY_PWA_MAIL_MAILER'),
            'host' => env('DEPLOY_PWA_MAIL_HOST'),
            'port' => env('DEPLOY_PWA_MAIL_PORT'),
            'username' => env('DEPLOY_PWA_MAIL_USERNAME'),
            'password' => env('DEPLOY_PWA_MAIL_PASSWORD'),
            'encryption' => env('DEPLOY_PWA_MAIL_ENCRYPTION'),
            'from_address' => env('DEPLOY_PWA_MAIL_FROM_ADDRESS'),
        ],
    ],
];
