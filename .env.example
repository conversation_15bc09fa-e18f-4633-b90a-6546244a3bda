APP_NAME=Deployer
APP_ENV=local
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=http://deployer.test

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=deployer
# DB_USERNAME=root
# DB_PASSWORD=

# TESTING_DB_HOST=127.0.0.1
# TESTING_DB_PORT=3306
# TESTING_DB_DATABASE=deployer_test
# TESTING_DB_USERNAME=root
# TESTING_DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

API_TOKEN=ca794fb2d950acf25c964ecc35f2d7e2

HETZNER_CLOUD_API_TOKEN=
HETZNER_CLOUD_API_URL=https://api.hetzner.cloud
HETZNER_CLOUD_NETWORK_NAMES=dilizy-dev-private
HETZNER_CLOUD_FIREWALL_NAMES=dilizy-dev-vms
HETZNER_CLOUD_SSH_KEY_NAMES=ideil,devops
HETZNER_CLOUD_SSH_KEY_FILE=/home/<USER>/.ssh/key
HETZNER_CLOUD_SERVER_NAME_TEMPLATE=dilizy-dev-%s
HETZNER_CLOUD_SERVER_TYPE_DMS=cx21
HETZNER_DNS_API_TOKEN=
HETZNER_DNS_API_URL=https://dns.hetzner.com/api
HETZNER_DNS_ZONE_NAME=dilizy.dev

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0

DEPLOY_ENVIRONMENT=stage

DEPLOY_ZBX_METADATA=

DEPLOY_DMS_ZONE_NAME=dilizy.run
DEPLOY_DMS_REGISTRY=registry.support.dev.dilizy.dev
DEPLOY_DMS_REGISTRY_USER=
DEPLOY_DMS_REGISTRY_PASSWORD=
DEPLOY_DMS_TAG=dev-bump-docker-36a8afa6
DEPLOY_DMS_ELASTICSEARCH_HOST=http://elasticsearch.internal.service.dev.dilizy.dev:9200
DEPLOY_DMS_ELASTICSEARCH_USER=
DEPLOY_DMS_ELASTICSEARCH_ENABLE_AUTH=false
DEPLOY_DMS_ELASTICSEARCH_GENERATE_PASSWORD=false
DEPLOY_DMS_ELASTICSEARCH_PASSWORD=
DEPLOY_DMS_SENTRY_DSN=https://<EMAIL>/id
DEPLOY_DMS_SENTRY_RELEASE=
DEPLOY_DMS_SENTRY_ENVIRONMENT=
DEPLOY_DMS_PDF_BASE_URL=
DEPLOY_DMS_AWS_URL=https://cdn.service.dev.dilizy.dev
DEPLOY_DMS_AWS_BUCKET=dms-dev-01
DEPLOY_DMS_AWS_ACCESS_KEY_ID=
DEPLOY_DMS_AWS_ACCESS_SECRET_KEY=
DEPLOY_DMS_MAIL_MAILER=smtp
DEPLOY_DMS_MAIL_HOST=smtp.gmail.com
DEPLOY_DMS_MAIL_PORT=587
DEPLOY_DMS_MAIL_USERNAME=<EMAIL>
DEPLOY_DMS_MAIL_PASSWORD=
DEPLOY_DMS_MAIL_ENCRYPTION=tls
DEPLOY_DMS_MAIL_FROM_ADDRESS="<EMAIL>"
DEPLOY_DMS_GOOGLE_MAPS_API_KEY=
DEPLOY_DMS_MARKETPLACES_ENCRYPTION_KEY=

DEPLOY_SENTRY_URL=https://sentry.monitoring.dilizy.dev
DEPLOY_SENTRY_DMS_PROJECT_NAME_TEMPLATE=dms-%s-dilizy-dev-hetzner
DEPLOY_SENTRY_PWA_PROJECT_NAME_TEMPLATE=pwa-%s-dilizy-dev-hetzner
DEPLOY_SENTRY_ORGANIZATION_SLUG=sentry
DEPLOY_SENTRY_TEAM_SLUG=dilizy-dev-hetzner
DEPLOY_SENTRY_AUTH_TOKEN=

DEPLOY_CHECKER_SSH_USER=root
DEPLOY_CHECKER_HOST=************** # dilizy-dev-additional
DEPLOY_CHECKER_URL=https://checker.additional.dev.dilizy.dev

DEPLOY_AI_SERVICE_SSH_USER=root
DEPLOY_AI_SERVICE_HOST=   # dilizy-dev-additional
DEPLOY_AI_SERVICE_URL=https://aiserviceadapter.dilizy.dev

DEPLOY_SCRM_SSH_USER=
DEPLOY_SCRM_HOST=
DEPLOY_SCRM_URL=https://scrm.dilizy.dev

DEPLOY_ENVELOPE_SSH_USER=
DEPLOY_ENVELOPE_HOST=
DEPLOY_ENVELOPE_URL=https://envelope.dilizy.dev

DEPLOY_CDN_URL=https://cdn.service.dev.dilizy.dev
DEPLOY_CDN_MINIO_HOST=cdn.service.dev.dilizy.dev
DEPLOY_CDN_BASIC_AUTH_USER=
DEPLOY_CDN_BASIC_AUTH_PASSWORD=
DEPLOY_CDN_CICD_ACCESS_KEY=
DEPLOY_CDN_CICD_SECRET_KEY=

DEPLOY_PWA_ZONE_NAME=dilizy.dev
DEPLOY_PWA_URL=
DEPLOY_PWA_API_CLIENT_ID=
DEPLOY_PWA_API_CLIENT_SECRET=
DEPLOY_PWA_ADMIN_URL=
DEPLOY_PWA_ADMIN_CLIENT_ID=
DEPLOY_PWA_ADMIN_SECRET=
DEPLOY_PWA_SENTRY_DSN=https://<EMAIL>/id
DEPLOY_PWA_SENTRY_RELEASE=
DEPLOY_PWA_SENTRY_ENVIRONMENT=
DEPLOY_PWA_REGISTRY=registry.support.dev.dilizy.dev
DEPLOY_PWA_REGISTRY_USER=
DEPLOY_PWA_REGISTRY_PASSWORD=
DEPLOY_PWA_VAPID_PUBLIC_KEY=
DEPLOY_PWA_VAPID_PRIVATE_KEY=
DEPLOY_PWA_TAG=
DEPLOY_PWA_MAIL_MAILER=smtp
DEPLOY_PWA_MAIL_HOST=smtp.gmail.com
DEPLOY_PWA_MAIL_PORT=587
DEPLOY_PWA_MAIL_USERNAME=<EMAIL>
DEPLOY_PWA_MAIL_PASSWORD=
DEPLOY_PWA_MAIL_ENCRYPTION=tls
DEPLOY_PWA_MAIL_FROM_ADDRESS="<EMAIL>"
