FROM php:8.2.25-fpm-bullseye@sha256:cce6b99f2691bbe659cc12358c9388fab9faa1223ee8d119852114eb0a476790

ENV DEBIAN_FRONTEND=noninteractive
WORKDIR /var/www/html/deployer

EXPOSE 9000
ENV NODE_MAJOR=20
RUN mkdir -p /usr/share/man/man1 && \
    apt-get update && \
    apt-get install -y ca-certificates curl gnupg && \
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
    apt-get update && \
    apt-get install -y nodejs

RUN apt-get update && \
    apt-get install -y \
      cron  \
      imagemagick \
      jpegoptim \
      libfreetype-dev \
      libjpeg62-turbo-dev \
      libmagickwand-dev \
      libpng-dev \
      libzip-dev \
      openssh-client \
      optipng \
      pngquant \
      rsync \
      supervisor  \
      unzip \
      zip \
    && \
    docker-php-ext-install zip && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j$(nproc) gd && \
    pecl install --force redis && \
    pecl install --force imagick && \
    rm -rf /tmp/pear && \
    docker-php-ext-enable redis && \
    docker-php-ext-enable imagick && \
    docker-php-ext-install bcmath mysqli pdo pdo_mysql pcntl && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN curl -sSL https://getcomposer.org/installer -o composer-setup.php && \
    php composer-setup.php --2.2 --install-dir=/usr/local/bin/ && \
    composer.phar --version

COPY --chown=www-data:www-data ./composer.* .
RUN composer.phar install --no-dev --no-plugins --no-scripts

COPY --chown=www-data:www-data ./package*.json ./
RUN npm ci

COPY --chown=www-data:www-data ./ ./
COPY --chown=www-data:www-data ./public/ /.init/public/
COPY --chown=www-data:www-data ./storage/ /.init/storage/
RUN chown -R www-data:www-data ./

COPY .dockerfiles/php/supervisord.conf /etc/supervisor/supervisord.conf
COPY .dockerfiles/php/deployer.conf /etc/supervisor/conf.d/deployer.conf
COPY .dockerfiles/php/zzz-docker.conf /usr/local/etc/php-fpm.d/zzz-docker.conf
COPY .dockerfiles/php/deployer.cron /var/spool/cron/crontabs/www-data
COPY .dockerfiles/php/docker-entrypoint.sh /

RUN chmod 600 /var/spool/cron/crontabs/www-data && \
    chown www-data:www-data /var/spool/cron/crontabs/www-data && \
    mkdir /var/log/fpm && \
    chown www-data:www-data /var/log/fpm && \
    mkdir /var/log/cron && \
    chown www-data:www-data /var/log/cron

ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["/usr/bin/supervisord"]
