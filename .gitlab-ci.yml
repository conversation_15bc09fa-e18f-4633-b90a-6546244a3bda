image: docker:27.1.1
services:
  - docker:27.1.1-dind

stages:
  - build_dev
  - build_stage
  - build_prod
  - deploy_dev
  - deploy_stage
  - deploy_prod

.docker_build_and_push: &docker_build_and_push
  before_script:
    - docker login -u $REGISTRY_USER -p $REGISTRY_PASSWORD $REGISTRY
    - docker context create mycontext || true
    - docker buildx create mycontext --use || true
  script:
    - export IMAGE_NAME="$REGISTRY/deployer/$MODULE_NAME"
    - export CACHE_TAG=cache-$CI_COMMIT_REF_SLUG
    - export CACHE_MAIN=cache-main
    - export TAG="$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"
    - docker pull $IMAGE_NAME:$CACHE_TAG || docker pull $IMAGE_NAME:$CACHE_MAIN || true
    - |
      docker buildx build . \
        --cache-to=type=inline \
        --cache-from=$IMAGE_NAME:$CACHE_TAG \
        --cache-from=$IMAGE_NAME:$CACHE_MAIN \
        --push \
        -f $MODULE_NAME.Dockerfile \
        -t $IMAGE_NAME:$TAG \
        -t $IMAGE_NAME:$CACHE_TAG
    - echo "$IMAGE_NAME:$TAG"

.run_on_dev: &run_on_dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: on_success

.run_on_stage: &run_on_stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "stage"'
      when: on_success

.run_on_prod: &run_on_prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_success

build_php_dev:
  stage: build_dev
  variables:
    MODULE_NAME: "php"
    REGISTRY: $DEV_REGISTRY
    REGISTRY_USER: $DEV_REGISTRY_USER
    REGISTRY_PASSWORD: $DEV_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_dev]

build_redis_dev:
  stage: build_dev
  variables:
    MODULE_NAME: "redis"
    REGISTRY: $DEV_REGISTRY
    REGISTRY_USER: $DEV_REGISTRY_USER
    REGISTRY_PASSWORD: $DEV_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_dev]

deploy_deployer_dev:
  stage: deploy_dev
  trigger:
    project: globalsmart.center/luxsteel/infrastructure/hetzner-infra
    branch: main
    strategy: depend
    forward:
      pipeline_variables: true
  needs:
    - build_php_dev
    - build_redis_dev
  <<: *run_on_dev
  variables:
    TRIGGER_313_DEV_deployer_auto_deploy: "true"
    TAG: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"

build_php_stage:
  stage: build_stage
  needs: []
  variables:
    MODULE_NAME: "php"
    REGISTRY: $STAGE_REGISTRY
    REGISTRY_USER: $STAGE_REGISTRY_USER
    REGISTRY_PASSWORD: $STAGE_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_stage]

build_redis_stage:
  stage: build_stage
  needs: []
  variables:
    MODULE_NAME: "redis"
    REGISTRY: $STAGE_REGISTRY
    REGISTRY_USER: $STAGE_REGISTRY_USER
    REGISTRY_PASSWORD: $STAGE_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_stage]

deploy_deployer_stage:
  stage: deploy_stage
  trigger:
    project: globalsmart.center/luxsteel/infrastructure/hetzner-infra
    branch: main
    strategy: depend
    forward:
      pipeline_variables: true
  needs:
    - build_php_stage
    - build_redis_stage
  variables:
    TRIGGER_313_STAGE_deployer_auto_deploy: "true"
    TAG: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"
  <<: *run_on_stage

build_php_prod:
  stage: build_prod
  variables:
    MODULE_NAME: "php"
    REGISTRY: $PROD_REGISTRY
    REGISTRY_USER: $PROD_REGISTRY_USER
    REGISTRY_PASSWORD: $PROD_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_prod]

build_redis_prod:
  stage: build_prod
  variables:
    MODULE_NAME: "redis"
    REGISTRY: $PROD_REGISTRY
    REGISTRY_USER: $PROD_REGISTRY_USER
    REGISTRY_PASSWORD: $PROD_REGISTRY_PASSWORD
  <<: [*docker_build_and_push, *run_on_prod]

deploy_deployer_prod:
  stage: deploy_prod
  trigger:
    project: globalsmart.center/luxsteel/infrastructure/hetzner-infra
    branch: main
    strategy: depend
    forward:
      pipeline_variables: true
  needs:
    - build_php_prod
    - build_redis_prod
  variables:
    TRIGGER_313_PROD_deployer_auto_deploy: "true"
    TAG: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"
  <<: *run_on_prod
