<?php

use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Execs\ApiCommandExec;
use App\System\Runtime\Execs\SshCommandExec;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Passport\Client;
use Lara<PERSON>\Passport\Passport;
use Tests\TestCase;

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

uses()
    ->beforeEach(function () {
        $sshCommandExec = Mockery::mock(SshCommandExec::class);
        $sshCommandExec
            ->shouldReceive('execute')
            ->withAnyArgs()
            ->zeroOrMoreTimes()
            ->andReturn(new CommandExecResult(null, null));
        $apiCommandExec = Mockery::mock(ApiCommandExec::class);
        $apiCommandExec
            ->shouldReceive('execute')
            ->withAnyArgs()
            ->zeroOrMoreTimes()
            ->andReturn(new CommandExecResult(null, null));

        // Mocking SSH and API command execution drivers (so that real drivers does not fire up during testing).
        $this->app->bind(SshCommandExec::class, fn () => $sshCommandExec);
        $this->app->bind(ApiCommandExec::class, fn () => $apiCommandExec);
    })
    ->in('Feature', 'Integration');

uses(TestCase::class, RefreshDatabase::class)
    ->group('feature')
    ->in('Feature');

uses(TestCase::class, RefreshDatabase::class)
    ->group('integration')
    ->in('Integration');

uses(TestCase::class)
    ->group('unit')
    ->in('Unit');

uses()
    ->beforeEach(function () {
        Passport::actingAsClient(Client::factory()->create());
    })
    ->group('api')
    ->in('Feature/Api');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function something()
{
    // ..
}
