<?php

use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStop;
use App\System\Installs\Stages\Implementations\Docker\Support\IssueDownCommand;
use App\System\Installs\Stages\Implementations\Docker\Support\IssueUpCommand;
use App\System\Runtime\Context;
use Mockery\MockInterface;

it('tries to stop running containers when run', function () {
    $instance = Instance::factory()->dms()->create();
    $context = new Context([
        'docker_compose_stop' => [
            'instance_id' => $instance->id,
            'env_file' => 'test-docker-compose.env',
            'yaml_file' => 'test-docker-compose.yaml',
        ],
    ]);
    $this->partialMock(IssueDownCommand::class, function (MockInterface $mock) {
        $mock
            ->shouldReceive('execute')
            ->withArgs(['test-docker-compose.env', 'test-docker-compose.yaml'])
            ->once();
    });

    $stage = new DockerComposeStop();
    $stage->provideName('Test docker compose stop');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('docker_compose_stop'));

    $stage->run();
});

it('tries to up existing containers without recreating when rolled back', function () {
    $instance = Instance::factory()->dms()->create();
    $context = new Context([
        'docker_compose_stop' => [
            'instance_id' => $instance->id,
            'env_file' => 'test-docker-compose.env',
            'yaml_file' => 'test-docker-compose.yaml',
        ],
    ]);
    $this->partialMock(IssueDownCommand::class, function (MockInterface $mock) {
        $mock
            ->shouldReceive('execute')
            ->withArgs(['test-docker-compose.env', 'test-docker-compose.yaml'])
            ->andThrow(new RuntimeException('Something went wrong during testing'));
    });
    $this->partialMock(IssueUpCommand::class, function (MockInterface $mock) {
        $mock
            ->shouldReceive('execute')
            ->withArgs(['test-docker-compose.env', 'test-docker-compose.yaml', false])
            ->once();
    });

    $stage = new DockerComposeStop();
    $stage->provideName('Test docker compose stop');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('docker_compose_stop'));

    try {
        $stage->run();
    } catch (RuntimeException $e) {
        if ($e->getMessage() === 'Something went wrong during testing') {
            $stage->rollback();
        }
    }
});

it('does nothing on rollback if the stage was not previously run', function () {
    $instance = Instance::factory()->dms()->create();
    $context = new Context([
        'docker_compose_stop' => [
            'instance_id' => $instance->id,
            'env_file' => 'test-docker-compose.env',
            'yaml_file' => 'test-docker-compose.yaml',
        ],
    ]);
    $this->partialMock(IssueUpCommand::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('execute');
    });

    $stage = new DockerComposeStop();
    $stage->provideName('Test docker compose stop');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('docker_compose_stop'));

    $stage->rollback();
});
