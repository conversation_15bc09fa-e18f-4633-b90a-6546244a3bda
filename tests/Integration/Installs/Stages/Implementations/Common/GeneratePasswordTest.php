<?php

use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Common\GeneratePassword;
use App\System\Runtime\Context;

it('generates password and writes it under specified key', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'test_password' => ['type' => 'password'],
            ],
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();

    expect($context->get('password_generation.test_password'))->not->toBeEmpty();
});

it('throws error when key_name is one of the reserved ones', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'title' => ['type' => 'password'],
            ],
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();
})->throws(InvalidArgumentException::class);

it('generates password based on provided specification', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'test_password' => ['type' => 'password', 'length' => 12, 'include' => ['numbers']],
            ]
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();
    $password = $context->get('password_generation.test_password');

    expect(strlen($password))->toBe(12);
    expect(preg_match('/^[0-9]+$/', $password))->toBe(1);
});

it('can generate laravel app key', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'app_key' => ['type' => 'laravel_app_key'],
            ],
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();

    expect($context->get('password_generation.app_key'))->not->toBeEmpty();
});

it('can generate cryptographically secure random hex strings', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'hex' => ['type' => 'random_bin2hex', 'length' => 16],
            ],
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();
    $password = $context->get('password_generation.hex');

    expect($password)->not->toBeEmpty();
    expect(strlen($password))->toBe(16);
});

it('can generate multiple passwords', function () {
    $context = new Context([
        'password_generation' => [
            'title' => 'Test',
            'key_names' => [
                'password1' => ['type' => 'password'],
                'password2' => ['type' => 'password'],
            ],
        ],
    ]);

    $stage = new GeneratePassword();
    $stage->provideName('Test password generation');
    $stage->provideContext($context);
    $stage->provideScope(new StageScope('password_generation'));

    $stage->run();

    expect($context->get('password_generation.password1'))->not->toBeEmpty();
    expect($context->get('password_generation.password2'))->not->toBeEmpty();
});
