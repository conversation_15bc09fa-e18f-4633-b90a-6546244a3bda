<?php

use App\Models\Infrastructure\Instance;
use App\Models\Versions\Version;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Stages\Implementations\Common\CheckUrlStatus;
use App\System\Installs\Stages\Implementations\Dms\StopAndUpgradeDms;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStart;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStop;
use App\System\Installs\Stages\Implementations\Docker\UpdateDockerComposeConfigs;
use App\System\Installs\Stages\Strategies\UpgradeDmsStrategy;
use Illuminate\Validation\ValidationException;

it('can validate incoming payload', function () {
    $strategy = new UpgradeDmsStrategy(new InstallPayload([
        'random_arg' => 'value',
    ], null));

    $strategy->assertItIsValid();
})->throws(ValidationException::class);

it('validates payload before getting stages', function () {
    $strategy = new UpgradeDmsStrategy(new InstallPayload([
        // empty payload
    ], null));

    $strategy->getStages();
})->throws(ValidationException::class);

it('returns correct stages for upgrading when payload is valid', function () {
    $instance = Instance::factory()->dms()->create();
    $newVersion = Version::factory()->dms()->create();

    $strategy = new UpgradeDmsStrategy(new InstallPayload([
        'dms_id' => $instance->id,
        'target_version_id' => $newVersion->id,
    ], null));
    $stages = $strategy->getStages();

    expect(count($stages->items))->toBe(5);
    expect($stages->items[0])->toBeInstanceOf(DockerComposeStop::class);
    expect($stages->items[1])->toBeInstanceOf(UpdateDockerComposeConfigs::class);
    expect($stages->items[2])->toBeInstanceOf(DockerComposeStart::class);
    expect($stages->items[3])->toBeInstanceOf(StopAndUpgradeDms::class);
    expect($stages->items[4])->toBeInstanceOf(CheckUrlStatus::class);
});
