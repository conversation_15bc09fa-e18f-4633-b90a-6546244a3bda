<?php

use App\Models\Installs\Install;
use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\StageStatus;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Factories\StagesStrategyFactory;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Mockery\MockInterface;

it('doesnt create install when stages strategy factory throws validation exception', function () {
    $strategyMock = $this->mock(StagesStrategy::class, function (MockInterface $mock) {
        $validator = Validator::make(['data' => 1], ['data2' => ['required']]);
        $mock
            ->shouldReceive('getStages')
            ->andThrowExceptions([new ValidationException($validator)]);
    });
    $this->mock(StagesStrategyFactory::class, function (MockInterface $mock) use ($strategyMock) {
        $mock
            ->shouldReceive('createForInstall')
            ->once()
            ->andReturn($strategyMock);
    });
    $payload = new InstallPayload(['data' => 1], null);

    $action = new StartInstall();
    $action
        ->provideType(InstallType::BASIC_DMS_SETUP)
        ->providePayload($payload);

    try {
        $action->execute();
    } catch (Throwable $e) {
        $this->assertDatabaseCount('installs', 0);

        throw $e;
    }
})->throws(ValidationException::class);

it('adds new install record with ordered stages', function () {
    $stage1Mock = $this->mock(StageImplementation::class, function (MockInterface $mock) {
        $mock->shouldReceive('getClass')->zeroOrMoreTimes()->andReturn('stage1Mock');
        $mock->shouldReceive('getScope')->zeroOrMoreTimes()->andReturn(new StageScope('stage1'));
        $mock->shouldReceive('getName')->zeroOrMoreTimes()->andReturn('stage1');
        $mock->shouldReceive('linkWithStage')->zeroOrMoreTimes();
        $mock->shouldNotReceive('run');
        $mock->shouldNotReceive('rollback');
    });
    $stage2Mock = $this->mock(StageImplementation::class, function (MockInterface $mock) {
        $mock->shouldReceive('getClass')->zeroOrMoreTimes()->andReturn('stage2Mock');
        $mock->shouldReceive('getScope')->zeroOrMoreTimes()->andReturn(new StageScope('stage2'));
        $mock->shouldReceive('getName')->zeroOrMoreTimes()->andReturn('stage2');
        $mock->shouldReceive('linkWithStage')->zeroOrMoreTimes();
        $mock->shouldNotReceive('run');
        $mock->shouldNotReceive('rollback');
    });
    $strategyMock = $this->mock(StagesStrategy::class, function (MockInterface $mock) use ($stage1Mock, $stage2Mock) {
        $mock
            ->shouldReceive('getStages')
            ->andReturn(new Stages([$stage1Mock, $stage2Mock], new Context(['name' => 'foo'])));
    });
    $this->mock(StagesStrategyFactory::class, function (MockInterface $mock) use ($strategyMock) {
        $mock
            ->shouldReceive('createForInstall')
            ->once()
            ->andReturn($strategyMock);
    });
    $payload = new InstallPayload(['data' => 1], null);

    $action = new StartInstall();
    $action
        ->provideType(InstallType::BASIC_DMS_SETUP)
        ->providePayload($payload);
    $action->execute();

    $this->assertDatabaseCount('installs', 1);
    $install = Install::first();
    expect($install->status)->toBe(InstallStatus::PENDING);
    expect($install->type)->toBe(InstallType::BASIC_DMS_SETUP);

    $this->assertDatabaseCount('stages', 2);

    $stage1 = $install->stages->get(0);
    expect($stage1->status)->toBe(StageStatus::PENDING);
    expect($stage1->order)->toBe(0);
    expect($stage1->name)->toBe('stage1');
    expect($stage1->prior_context)->toBe(['name' => 'foo']);
    expect($stage1->resulting_context)->toBeNull();
    expect($stage1->implementation)->not->toBeNull();

    $stage2 = $install->stages->get(1);
    expect($stage2->status)->toBe(StageStatus::PENDING);
    expect($stage2->order)->toBe(1);
    expect($stage2->name)->toBe('stage2');
    expect($stage2->prior_context)->toBeNull();
    expect($stage2->resulting_context)->toBeNull();
    expect($stage2->implementation)->not->toBeNull();
});
