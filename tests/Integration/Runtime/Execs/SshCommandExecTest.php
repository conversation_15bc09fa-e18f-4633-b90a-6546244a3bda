<?php

use App\Models\Infrastructure\Connections\SshConnection;
use App\System\Runtime\Execs\SshCommandExec;

it('can be created from SSH connection model', function () {
    $sshConnection = SshConnection::factory()->create([
        'host' => '127.0.0.1',
        'user' => 'root',
        'private_key' => '/home/<USER>/.ssh/key',
        'timeout' => 5,
        'remove_bash' => false,
    ]);

    $exec = SshCommandExec::createFromModel($sshConnection);

    expect($exec)->getHost()->toBe('127.0.0.1');
    expect($exec)->getUser()->toBe('root');
    expect($exec)->getPrivateKey()->toBe('/home/<USER>/.ssh/key');
    expect($exec)->getTimeout()->toBe(5);
    expect($exec)->getRemoveBash()->toBe(false);
});
