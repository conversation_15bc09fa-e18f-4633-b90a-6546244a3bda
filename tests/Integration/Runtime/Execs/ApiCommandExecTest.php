<?php

use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\ApiMethod;
use App\System\Runtime\Execs\ApiCommandExec;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;

it('sends GET requests and returns results', function () {
    Http::preventStrayRequests();
    Http::fake([
        'https://request.test/get?foo=bar' => Http::response(['response' => 'value'], 200),
    ]);

    $exec = new ApiCommandExec();
    $result = $exec->execute(new ApiCommand(
        'https://request.test/get',
        ApiMethod::GET,
        ['foo' => 'bar'],
        ['X-Header' => 'value'],
    ));

    Http::assertSentCount(1);
    Http::assertSent(function (Request $request) {
        return
            $request->hasHeader('X-Header', 'value') &&
            $request->url() === 'https://request.test/get?foo=bar';
    });

    expect($result)->error->toBeEmpty();
    expect($result)->payload->toMatchArray([
        'response' => 'value',
    ]);
});

it('sends POST requests and returns results', function () {
    Http::preventStrayRequests();
    Http::fake([
        'https://request.test/post' => Http::response(['response' => 'value'], 201),
    ]);

    $exec = new ApiCommandExec();
    $result = $exec->execute(new ApiCommand(
        'https://request.test/post',
        ApiMethod::POST,
        ['foo' => 'bar'],
    ));

    Http::assertSentCount(1);
    Http::assertSent(function (Request $request) {
        return
            $request->method() === 'POST' &&
            $request->url() === 'https://request.test/post' &&
            $request['foo'] === 'bar';
    });

    expect($result)->error->toBeEmpty();
    expect($result)->payload->toMatchArray([
        'response' => 'value',
    ]);
});

it('sends DELETE requests and returns results', function () {
    Http::preventStrayRequests();
    Http::fake([
        'https://request.test/delete' => Http::response(null, 204),
    ]);

    $exec = new ApiCommandExec();
    $result = $exec->execute(new ApiCommand(
        'https://request.test/delete',
        ApiMethod::DELETE,
        ['foo' => 'bar'],
    ));

    Http::assertSentCount(1);
    Http::assertSent(function (Request $request) {
        return
            $request->method() === 'DELETE' &&
            $request->url() === 'https://request.test/delete' &&
            $request['foo'] === 'bar';
    });

    expect($result)->error->toBeEmpty();
    expect($result)->payload->toBeEmpty();
});

it('returns error result on non-2xx response codes with response in payload', function () {
    Http::preventStrayRequests();
    Http::fake([
        'https://request.test/post' => Http::response(['error' => 'foo is missing'], 422),
    ]);

    $exec = new ApiCommandExec();
    $result = $exec->execute(new ApiCommand(
        'https://request.test/post',
        ApiMethod::POST,
        [],
    ));

    Http::assertSentCount(1);
    Http::assertSent(function (Request $request) {
        return
            $request->method() === 'POST' &&
            $request->url() === 'https://request.test/post';
    });

    expect($result)->error->not->toBeEmpty();
    expect($result)->payload->toMatchArray([
        'error' => 'foo is missing',
    ]);
    expect($result)->error->code->toBe(422);
    expect($result)->error->data->toMatchArray([
        'error' => 'foo is missing',
    ]);
});
