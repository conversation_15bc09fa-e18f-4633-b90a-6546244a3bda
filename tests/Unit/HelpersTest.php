<?php

test('template helper correctly forms strings with parameters', function () {
    $template = 'This is param value = :param';

    expect(template($template, ['param' => 'value']))->toBe('This is param value = value');
});

test('template helper does not generate exception if some parameters are left unused', function () {
    $template = 'This is param value = :param';

    expect(template($template, [
        'param' => 'value',
        'param2' => 'unused',
    ]))->toBe('This is param value = value');
});

test('template helper does not generate exception if some parameters are not specified', function () {
    $template = 'This is param value = :param';

    expect(template($template, [
        'param2' => 'unused',
    ]))->toBe('This is param value = :param');
});
