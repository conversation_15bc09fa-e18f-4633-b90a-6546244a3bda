<?php

use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Execs\SshCommandExec;
use Spatie\Ssh\Ssh;
use Symfony\Component\Process\Process;

it('can be created with host, user and private key', function () {
    new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key');
})->throwsNoExceptions();

it('can be provided additional settings', function () {
    $exec = new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key');

    $exec->setTimeout(2)->removeBash(false);
})->throwsNoExceptions();

it('cannot be provided invalid timeout value', function () {
    $exec = new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key');

    $exec->setTimeout(-2);
})->throws(InvalidArgumentException::class);

it('only understands SSH commands', function () {
    $exec = new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key');
    $command = Mockery::mock(Command::class);

    $exec->execute($command);
})->throws(LogicException::class);

it('returns correct result if process returns zero exit code', function () {
    $command = new SshCommand('test command');

    $process = Mockery::mock(Process::class);
    $process->shouldReceive('getIncrementalOutput')->andReturn('success');
    $process->shouldReceive('getIncrementalErrorOutput')->andReturn('');
    $process->shouldReceive('getExitCode')->andReturn(0);

    $ssh = Mockery::mock(Ssh::class);
    $ssh->shouldReceive('execute')->withArgs(['test command'])->andReturn($process);
    $ssh->shouldReceive('usePrivateKey')->withArgs(['/home/<USER>/.ssh/key'])->andReturnSelf();
    $ssh->shouldReceive('disableStrictHostKeyChecking')->andReturnSelf();
    $ssh->shouldReceive('disablePasswordAuthentication')->andReturnSelf();
    $ssh->shouldReceive('configureProcess')->andReturnSelf();
    $ssh->shouldReceive('removeBash')->andReturnSelf();

    $this->app->bind(Ssh::class, fn () => $ssh);

    $exec = (new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key'))
        ->removeBash(true);

    $result = $exec->execute($command);

    expect($result->error)->toBeEmpty();
    expect($result->payload)->toMatchArray(['stdout' => 'success']);
});

it('returns error result if process returns non-zero exit code', function () {
    $command = new SshCommand('test command');

    $process = Mockery::mock(Process::class);
    $process->shouldReceive('getIncrementalOutput')->andReturn('');
    $process->shouldReceive('getIncrementalErrorOutput')->andReturn('unknown error');
    $process->shouldReceive('getExitCode')->andReturn(1);

    $ssh = Mockery::mock(Ssh::class);
    $ssh->shouldReceive('execute')->withArgs(['test command'])->andReturn($process);
    $ssh->shouldReceive('usePrivateKey')->withArgs(['/home/<USER>/.ssh/key'])->andReturnSelf();
    $ssh->shouldReceive('disableStrictHostKeyChecking')->andReturnSelf();
    $ssh->shouldReceive('disablePasswordAuthentication')->andReturnSelf();
    $ssh->shouldReceive('configureProcess')->andReturnSelf();
    $ssh->shouldReceive('removeBash')->andReturnSelf();

    $this->app->bind(Ssh::class, fn () => $ssh);

    $exec = new SshCommandExec('127.0.0.1', 'root', '/home/<USER>/.ssh/key');

    $result = $exec->execute($command);

    expect($result->error)->not->toBeEmpty();
    expect($result->error)->message->toBe('unknown error');
    expect($result->error)->code->toBe(1);
});
