<?php

use App\System\Runtime\IO\Data\ParsingResult;

/**
 * Initialization tests.
 */
it('cannot be initialized with both error and value', function () {
    new ParsingResult('<PERSON>', 'Error when searching for user name!');
})->throws(LogicException::class);

/**
 * Main tests.
 */
test('checking if it contains successfull result', function () {
    $successfullResult = new ParsingResult(null, errorText: 'Failed to parse!');
    expect($successfullResult->isSuccessfull())->toBeFalse();

    $successfullResult = new ParsingResult('<PERSON>', errorText: null);
    expect($successfullResult->isSuccessfull())->toBeTrue();
});
