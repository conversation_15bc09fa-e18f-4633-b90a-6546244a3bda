<?php

use App\System\Runtime\IO\Data\InputData;
use App\System\Runtime\IO\Data\InputDataBag;
use App\System\Runtime\IO\Exceptions\InputDataDuplicationException;

/**
 * Initialization tests.
 */
it('cannot contain multiple inputs with same name', function () {
    $input1 = new InputData('name');
    $input2 = new InputData('name');

    new InputDataBag($input1, $input2);
})->throws(InputDataDuplicationException::class);

it('can be empty', function () {
    new InputDataBag();
})->throwsNoExceptions();

test('empty factory method creating empty bag', function () {
    $emptyBag = InputDataBag::empty();

    expect($emptyBag->isEmpty())->toBeTrue();
});

/**
 * Main tests.
 */
test('checking its emptiness', function () {
    $emptyBag = new InputDataBag();
    $nonEmptyBag = new InputDataBag(new InputData('name'));

    expect($emptyBag->isEmpty())->toBeTrue();
    expect($nonEmptyBag->isEmpty())->toBeFalse();
});

test('getting its items when it is empty', function () {
    $emptyBag = new InputDataBag();

    expect($emptyBag->getItems())->toBeEmpty();
});

it('maps items by name', function () {
    $input = new InputData('id');
    $nonEmptyBag = new InputDataBag($input);

    expect($nonEmptyBag->getItems())->toMatchArray(['id' => $input]);
});
