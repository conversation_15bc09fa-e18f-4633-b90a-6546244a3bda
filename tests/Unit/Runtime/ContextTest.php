<?php

use App\System\Runtime\Context;

/**
 * Initialization tests.
 */
it('accepts no initial values by default', function () {
    new Context();
})->throwsNoExceptions();

it('accepts array of strings as initial values', function () {
    new Context([
        'key1' => 'value 1',
        'key2' => 'other value',
    ]);
})->throwsNoExceptions();

it('accepts non-string scalar values as initial ones', function () {
    new Context([
        'key1' => 3,
        'key2' => true,
    ]);
})->throwsNoExceptions();

it('accepts array values as initial ones', function () {
    new Context([
        'key' => ['value1', 'value2'],
    ]);
})->throwsNoExceptions();

it('does not accept object values as initial ones', function () {
    new Context([
        'key' => new stdClass(['foo' => 'bar']),
    ]);
})->throws(TypeError::class);

it('accepts Stringable values as initial ones', function () {
    new Context([
        'key' => new class () implements Stringable {
            public function __toString(): string
            {
                return 'value';
            }
        },
    ]);
})->throwsNoExceptions();

it('does not accept non-string keys as initial values', function () {
    new Context([
        0 => 'value 1',
        'correct_key' => 'other value',
    ]);
})->throws(InvalidArgumentException::class);

/**
 * Main tests.
 */
it('returns null when accessing non-existing key', function () {
    $context = new Context();
    $context->set('key1', 'value');

    expect($context->get('key2'))->toBeNull();
});

it('allows to set and get string value by key', function () {
    $context = new Context();
    $context->set('key', 'value');

    expect($context->get('key'))->toBe('value');
});

it('allows overriding existing values by key', function () {
    $context = new Context();
    $context->set('key', 'value');
    $context->set('key', 'new value');

    expect($context->get('key'))->toBe('new value');
});
