<?php

use App\System\Runtime\Commands\Data\CommandExecError;
use App\System\Runtime\Commands\Data\CommandExecResult;

/**
 * Initialization tests.
 */
it('can be initialized with both error and payload', function () {
    new CommandExecResult(['message' => 'Executing...'], new CommandExecError('Failed to execute', 1));
})->throwsNoExceptions();

/**
 * Main tests.
 */
test('checking if it contains successfull result', function () {
    $errorResult = new CommandExecResult(['message' => 'Executing...'], new CommandExecError('Failed to execute', 1));
    expect($errorResult->isSuccessfull())->toBeFalse();

    $successfullResult = new CommandExecResult(['message' => 'Executing... DONE!'], error: null);
    expect($successfullResult->isSuccessfull())->toBeTrue();

    $successfullEmptyResult = new CommandExecResult(payload: null, error: null);
    expect($successfullEmptyResult->isSuccessfull())->toBeTrue();
});
