<?php

use App\System\Installs\Data\InstallStatus;

test('only finished, failed and canceled statuses are final', function () {
    expect(InstallStatus::PENDING)->isFinal()->not->toBeTrue();
    expect(InstallStatus::RUNNING)->isFinal()->not->toBeTrue();
    expect(InstallStatus::CANCELED)->isFinal()->toBeTrue();
    expect(InstallStatus::FINISHED)->isFinal()->toBeTrue();
    expect(InstallStatus::FAILED)->isFinal()->toBeTrue();
});
