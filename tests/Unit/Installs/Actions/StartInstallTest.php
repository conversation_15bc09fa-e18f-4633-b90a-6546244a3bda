<?php

use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallType;

/**
 * Initialization tests.
 */
it('accepts type', function () {
    $action = new StartInstall();
    $action->provideType(InstallType::BASIC_DMS_SETUP);
})->throwsNoExceptions();

it('accepts payload', function () {
    $action = new StartInstall();
    $action->providePayload(new InstallPayload(['some_data'], null));
})->throwsNoExceptions();
