<?php

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\Console\Exception\RuntimeException;

it('creates user with given attributes', function () {
    $this
        ->artisan('app:support:register-<NAME_EMAIL> testpassword')
        ->assertSuccessful();
    $this
        ->assertDatabaseHas('users', [
            'name' => 'test',
            'email' => '<EMAIL>',
        ])
        ->assertDatabaseCount('users', 1);

    $user = User::first();
    expect(Hash::check('testpassword', $user->password))->toBeTrue();
});

it('does not register user without all arguments', function () {
    $this->artisan('app:support:register-user <EMAIL> testpassword');
})->throws(RuntimeException::class);

it('does not register user with empty password', function () {
    $this
        ->artisan('app:support:register-<NAME_EMAIL> ""')
        ->assertFailed();
});

it('does not register user with empty email', function () {
    $this
        ->artisan('app:support:register-user Test "" testpassword')
        ->assertFailed();
});

it('does not register user with empty name', function () {
    $this
        ->artisan('app:support:register-user "" <EMAIL> testpassword')
        ->assertFailed();
});

it('validates arguments and does not register user when validation fails', function () {
    $this
        ->artisan('app:support:register-user Test not_an_email testpassword')
        ->assertFailed();

    $this->assertDatabaseCount('users', 0);
});

it('does not register user when his email is already taken', function () {
    User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this
        ->artisan('app:support:register-<NAME_EMAIL> testpassword')
        ->assertFailed();
});
