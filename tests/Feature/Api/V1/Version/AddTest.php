<?php


test('cant add version without using API token authentication', function () {
    $response = $this->postJson('api/v1/version', [
        'app_type' => 'pwa',
        'name' => 'injection',
        'tag' => 'injection',
        'description' => 'injection',
        'docker_compose_yaml' => base64_encode('injection file content'),
        'docker_compose_env' => base64_encode('injection file content'),
    ]);

    $response->assertStatus(401);
    $this->assertDatabaseCount('versions', 0);
});

test('cant add version using wrong API token for authentication', function () {
    $response = $this
        ->withHeaders([
            'Authorization' => sprintf('Bearer %s', 'incorrect_api_token'),
        ])
        ->postJson('api/v1/version', [
            'app_type' => 'pwa',
            'name' => 'injection',
            'tag' => 'injection',
            'description' => 'injection',
        ]);

    $response->assertStatus(401);
    $this->assertDatabaseCount('versions', 0);
});

test('can add version using API token authentication', function () {
    $response = $this
        ->withHeaders([
            'Authorization' => sprintf('Bearer %s', 'correct_api_token'),
        ])
        ->postJson('api/v1/version', [
            'app_type' => 'dms',
            'name' => 'v1.0',
            'tag' => 'main-1-0',
            'description' => 'Main version description',
        ]);

    $response->assertStatus(201);
    $this->assertDatabaseHas('versions', [
        'app_type' => 'dms',
        'name' => 'v1.0',
        'tag' => 'main-1-0',
        'description' => 'Main version description',
    ]);
    $this->assertDatabaseCount('versions', 1);
});

it('uses base64 decoding for docker configs', function () {
    $response = $this
        ->withHeaders([
            'Authorization' => sprintf('Bearer %s', 'correct_api_token'),
        ])
        ->postJson('api/v1/version', [
            'app_type' => 'dms',
            'name' => 'v1.0',
            'tag' => 'main-1-0',
            'description' => 'Main version description',
            'docker_compose_yaml' => base64_encode("docker compose: config\nversion: 1.0"),
            'docker_compose_env' => base64_encode("ENV=test\nATTRIBUTE=value"),
        ]);

    $response->assertStatus(201);
    $this->assertDatabaseHas('versions', [
        'app_type' => 'dms',
        'name' => 'v1.0',
        'tag' => 'main-1-0',
        'description' => 'Main version description',
        'docker_compose_yaml' => "docker compose: config\nversion: 1.0",
        'docker_compose_env' => "ENV=test\nATTRIBUTE=value",
    ]);
    $this->assertDatabaseCount('versions', 1);
});
