<?php

use App\Models\Installs\Install;

test('install list is accessible and empty from scratch', function () {
    $response = $this->getJson('api/v1/install');

    $response
        ->assertStatus(200)
        ->assertJson([
            'data' => [],
        ]);
});

test('install list provides a paginated output of available installs with 15 per page', function () {
    Install::factory()->count(20)->create();

    $response = $this->getJson('api/v1/install');

    $response
        ->assertStatus(200)
        ->assertJsonPath('meta.per_page', 15)
        ->assertJsonPath('meta.prev_cursor', null);
});

test('install list can be traversed via cursor pagination', function () {
    Install::factory()->count(17)->create();

    $response1 = $this->getJson('api/v1/install');
    $response1
        ->assertStatus(200)
        ->assertJsonCount(15, 'data')
        ->assertJsonPath('meta.prev_cursor', null)
        ->assertJsonPath('meta.next_cursor', fn ($cursor) => ! empty($cursor));

    $response2 = $this->getJson(sprintf('api/v1/install?cursor=%s', $response1->json('meta.next_cursor')));
    $response2
        ->assertStatus(200)
        ->assertJsonCount(2, 'data')
        ->assertJsonPath('meta.prev_cursor', fn ($cursor) => ! empty($cursor))
        ->assertJsonPath('meta.next_cursor', null);
});

test('install list can be filtered by status', function () {
    Install::factory()->create();
    $running = Install::factory()->running()->create();

    $response1 = $this->getJson('api/v1/install');
    $response1
        ->assertStatus(200)
        ->assertJsonCount(2, 'data');

    $response2 = $this->getJson(sprintf('api/v1/install?status=%s', 'running'));
    $response2
        ->assertStatus(200)
        ->assertJsonCount(1, 'data');

    expect($response2->json('data.0.id'))->toBe($running->id);
    expect($response2->json('data.0.status'))->toBe('running');
});
