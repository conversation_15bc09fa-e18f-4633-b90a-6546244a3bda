<?php

use App\Models\Installs\Install;

test('existing install can be viewed via dedicated API endpoint', function () {
    $install = Install::factory()->create();
    $response = $this->getJson(sprintf('api/v1/install/%d', $install->id));

    $response
        ->assertStatus(200)
        ->assertJsonPath('data.id', $install->id);
});

test('view API endpoint returns 404 response for unexisting installs', function () {
    $response = $this->getJson(sprintf('api/v1/install/%d', 231));

    $response
        ->assertStatus(404);
});
