<?php

use Illuminate\Testing\Fluent\AssertableJson;

it('throws validation error for unexisting type', function () {
    $response = $this->postJson('api/v1/install', [
        'type' => 'unexisting_foo_type',
        'data' => [
            'some' => 'data',
        ],
    ]);

    $response
        ->assertStatus(422)
        ->assertJson(
            fn (AssertableJson $json) => $json->has('errors.type')->etc(),
        );
});
