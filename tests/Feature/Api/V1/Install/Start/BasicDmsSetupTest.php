<?php

use App\Jobs\Installs\RunInstall;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Testing\Fluent\AssertableJson;

test('basic DMS setup install can be initiated via API', function () {
    Queue::fake();
    Http::fake();

    $response = $this->postJson('api/v1/install', [
        'type' => 'basic_dms_setup',
        'data' => [
            'subdomain' => 'test',
            'with_pwa' => false,
            'email' => '<EMAIL>',
            'company_name' => 'Test',
        ],
    ]);

    $response
        ->assertStatus(201)
        ->assertJson(
            fn (AssertableJson $json) =>
            $json->has(
                'data',
                fn (AssertableJson $json) =>
                $json
                    ->hasAll(['id', 'type', 'status', 'ended_at', 'created_at'])
                    ->has(
                        'stages',
                        9,
                        fn (AssertableJson $json) =>
                        $json->hasAll(['status', 'name', 'type', 'order', 'ended_at', 'created_at'])->etc()
                    )
                    ->etc()
            )
        );

    Queue::assertPushed(RunInstall::class);
});

test('basic DMS setup install requires subdomain data', function () {
    Queue::fake();
    Http::fake();

    $response = $this->postJson('api/v1/install', [
        'type' => 'basic_dms_setup',
        'data' => [
            'email' => '<EMAIL>',
            'company_name' => 'Test',
            'with_pwa' => false,
        ],
    ]);

    $response
        ->assertStatus(422)
        ->assertJson(
            fn (AssertableJson $json) =>
            $json
                ->has(
                    'errors',
                    fn (AssertableJson $json) =>
                    $json
                        ->has('data.subdomain')
                        ->etc()
                )
                ->etc()
        );

    Queue::assertNothingPushed();
});

test('basic DMS setup install can be initiated without PWA attribute', function () {
    Queue::fake();
    Http::fake();

    $response = $this->postJson('api/v1/install', [
        'type' => 'basic_dms_setup',
        'data' => [
            'email' => '<EMAIL>',
            'company_name' => 'Test',
            'subdomain' => 'test',
        ],
    ]);

    $response
        ->assertStatus(201);

    Queue::assertPushed(RunInstall::class);
});
