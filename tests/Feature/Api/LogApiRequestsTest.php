<?php

use App\Http\Middleware\LogApiRequests;
use App\Models\Logs\ApiRequestLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\Client;
use Lara<PERSON>\Passport\Passport;
use Symfony\Component\HttpFoundation\Response;

it('logs incoming GET API request once with response body', function () {
    $this->getJson('api/v1/install');
    $logRecord = ApiRequestLog::first();

    $this->assertDatabaseHas('api_request_logs', [
        'request_url' => url('api/v1/install'),
        'request_method' => 'GET',
        'response_http_code' => 200,
    ]);
    $this->assertDatabaseCount('api_request_logs', 1);
    expect($logRecord->response_body)->not->toBeEmpty();
});

it('logs incoming 404 GET API request once', function () {
    $this->getJson('api/v1/foo/bar/baz');

    $this->assertDatabaseHas('api_request_logs', [
        'request_url' => url('api/v1/foo/bar/baz'),
        'request_method' => 'GET',
        'response_http_code' => 404,
    ]);
    $this->assertDatabaseCount('api_request_logs', 1);
});

it('logs unexpected errors once with response body', function () {
    Route::middleware([LogApiRequests::class])
        ->post('api/error-test', function () {
            throw new Exception('Some error occured!');
        });

    $this->postJson('api/error-test');
    $logRecord = ApiRequestLog::first();

    $this->assertDatabaseHas('api_request_logs', [
        'request_url' => url('api/error-test'),
        'request_method' => 'POST',
        'response_http_code' => 500,
    ]);
    $this->assertDatabaseCount('api_request_logs', 1);
    expect($logRecord->response_body)->not->toBeEmpty();
});

it('logs unexpected errors with other middleware with response body', function () {
    Route::middleware([LogApiRequests::class, BuggyMiddleware::class])
        ->post('api/error-middleware-test', function () {
            return response()->noContent();
        });

    $this->postJson('api/error-middleware-test');
    $logRecord = ApiRequestLog::first();

    $this->assertDatabaseHas('api_request_logs', [
        'request_url' => url('api/error-middleware-test'),
        'request_method' => 'POST',
        'response_http_code' => 500,
    ]);
    $this->assertDatabaseCount('api_request_logs', 1);
    expect($logRecord->response_body)->not->toBeEmpty();
});

it('logs same incoming GET API requests individually', function () {
    $this->getJson('api/v1/install');
    $this->getJson('api/v1/install');

    $this->assertDatabaseCount('api_request_logs', 2);
});

it('logs Laravel Passport client', function () {
    $client1 = Passport::actingAsClient(Client::factory()->create());
    $this->getJson('api/v1/install');
    $logRecord1 = ApiRequestLog::first();

    $client2 = Passport::actingAsClient(Client::factory()->create());
    $this->getJson('api/v1/install');
    $logRecord2 = ApiRequestLog::orderBy('id', 'desc')->first();

    expect($logRecord1->model->is($client1))->toBeTrue();
    expect($logRecord2->model->is($client2))->toBeTrue();
});

class BuggyMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        throw new Exception('Some error occured!');
    }
}
