<?php

use App\Models\Logs\ExternalRequestLog;
use Illuminate\Support\Facades\Http;
use Lara<PERSON>\Passport\Client;
use Laravel\Passport\Passport;

it('logs external GET API request once', function () {
    Http::fake();

    Http::get('some-external-service.test');
    $this->assertDatabaseHas('external_request_logs', [
        'request_url' => 'some-external-service.test',

    ]);

    $this->assertDatabaseCount('external_request_logs', 1);
});

it('logs external POST API request once with body and response', function () {
    Http::fake([
        'some-external-service.test' => Http::response(['data' => ['result' => 'ok']], 201),
    ]);

    Http::post('some-external-service.test', ['name' => 'Test', 'status' => 1]);

    $logRecord = ExternalRequestLog::first();
    $this->assertDatabaseHas('external_request_logs', [
        'request_url' => 'some-external-service.test',
        'request_method' => 'POST',
        'response_http_code' => 201,
    ]);
    $this->assertDatabaseCount('external_request_logs', 1);
    expect($logRecord->request_body)->toBe(['name' => 'Test', 'status' => 1]);
    expect($logRecord->response_body)->toBe(['data' => ['result' => 'ok']]);
});

it('logs error external POST API request once', function () {
    Http::fake([
        'some-external-service.test' => Http::response(null, 500),
    ]);

    Http::post('some-external-service.test');

    $this->assertDatabaseHas('external_request_logs', [
        'request_url' => 'some-external-service.test',
        'request_method' => 'POST',
        'response_http_code' => 500,
        'response_body' => null,
    ]);
    $this->assertDatabaseCount('external_request_logs', 1);
});

it('logs Laravel Passport client', function () {
    $newClient = Passport::actingAsClient(Client::factory()->create());
    Http::fake();

    Http::get('some-external-service.test');

    $logRecord = ExternalRequestLog::first();
    $this->assertDatabaseCount('external_request_logs', 1);

    expect($logRecord->model->is($newClient))->toBeTrue();
});
