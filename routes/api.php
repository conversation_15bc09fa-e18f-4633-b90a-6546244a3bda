<?php

use App\Http\Controllers\Api\OAuth\AccessTokenController;
use App\Http\Middleware\LogApiRequests;
use Illuminate\Support\Facades\Route;

Route::middleware([LogApiRequests::class])
    ->name('api.')
    ->group(function () {
        require __DIR__ . '/api/v1.php';
    });

/**
 * Passport OAuth routes.
 */
Route::namespace('App\Http\Controllers\Api\OAuth')
    ->name('passport.')
    ->prefix('oauth')
    ->group(function () {
        Route::post('token', [AccessTokenController::class, 'issueToken'])
            ->middleware('throttle')
            ->name('token');
    });
