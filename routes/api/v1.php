<?php

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Controllers\Api\V1\Info\InfoController;
use App\Http\Controllers\Api\V1\Installs\InstallController;
use App\Http\Controllers\Api\V1\Instances\InstanceController;
use App\Http\Controllers\Api\V1\Instances\SubscriptionController;
use App\Http\Controllers\Api\V1\Versions\VersionController;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\Http\Middleware\CheckClientCredentials;

Route::prefix('v1')
    ->name('v1.')
    ->group(function () {
        Route::middleware(['auth:direct-api'])->group(function () {
            Route::post('version', [VersionController::class, 'add'])->name('versions.add');
        });

        Route::middleware([CheckClientCredentials::class])->group(function () {
            Route::get('install', [InstallController::class, 'list'])->name('install.list');
            Route::post('install', [InstallController::class, 'start'])->name('install.start');
            Route::get('install/{install}', [InstallController::class, 'view'])->name('install.view');

            /**
             * SCRM compatible routes.
             */
            Route::get('info/releases', [InfoController::class, 'releases'])->name('info.releases');
            Route::get('instances/{instance}', [InstanceController::class, 'view'])->name('instance.view');
            Route::delete('instances/{instance}', [InstanceController::class, 'delete'])->name('instance.delete');
            Route::post('instances/{instance}/releases', [InstanceController::class, 'releaseStart'])
                ->name('instance.release.start');
            Route::get('instances/{instance}/releases/{install}', [InstanceController::class, 'releaseView'])
                ->name('instance.release.view');
            Route::post('instances/limits/stats', [InstanceController::class, 'updateLimitsStats'])
                ->name('instance.update-limits-stats');

			Route::post('subscriptions', [SubscriptionController::class, 'updateSubscription'])
				->name('instance.update-subscription');

            Route::any('{query}', [ApiController::class, 'wrongUrl'])
                ->where('query', '.+')
                ->name('wrong-url');
        });
    });
