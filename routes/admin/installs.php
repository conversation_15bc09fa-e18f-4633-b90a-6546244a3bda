<?php

use App\Http\Controllers\Admin\InstallsController;
use Illuminate\Support\Facades\Route;

Route::prefix('installs')->group(function () {
    Route::get('list', [InstallsController::class, 'list'])->name('admin.installs.list');
    Route::get('{install}', [InstallsController::class, 'view'])->name('admin.installs.view');

    /* API routes */
    Route::get('api/list', [InstallsController::class, 'apiList'])->name('admin.installs.api-list');
});
