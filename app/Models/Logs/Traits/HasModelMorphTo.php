<?php

namespace App\Models\Logs\Traits;

use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Passport\Guards\TokenGuard as PassportTokenGuard;

trait HasModelMorphTo
{
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function tryAssigningModelFromApiGuard(Request $request): void
    {
        $user = $request->user('api');
        if ($user) {
            $this->model()->associate($user);
        } else {
            $guard = Auth::guard('api');
            if ($guard instanceof PassportTokenGuard) {
                $this->model()->associate($guard->client());
            }
        }
    }
}
