<?php

namespace App\Models\Logs;

use App\Models\Logs\Traits\HasModelMorphTo;
use Illuminate\Database\Eloquent\Model;

class ExternalRequestLog extends Model
{
    use HasModelMorphTo;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'request_headers' => 'array',
            'request_body' => 'array',
            'response_body' => 'array',
            'response_headers' => 'array',
        ];
    }
}
