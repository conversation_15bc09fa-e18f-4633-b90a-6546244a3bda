<?php

namespace App\Models\Logs;

use App\Models\Logs\Traits\HasModelMorphTo;
use App\System\Runtime\Commands\Data\CommandType;
use Illuminate\Database\Eloquent\Model;

class CommandLog extends Model
{
    use HasModelMorphTo;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'type' => CommandType::class,
            'payload' => 'array',
            'result' => 'array',
        ];
    }
}
