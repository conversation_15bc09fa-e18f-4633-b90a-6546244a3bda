<?php

namespace App\Models\Versions;

use App\System\Versions\Data\VersionStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Traits\HasStatuses;
use App\System\Apps\Data\AppType;

class Version extends Model
{
    use HasStatuses;
    use HasFactory;
    use SoftDeletes;

    protected $statusEnumClass = VersionStatus::class;
    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'app_type' => AppType::class,
        ];
    }

    public function isValidStatus(string $name, ?string $reason = null): bool
    {
        return in_array(VersionStatus::from($name), $this->status->validNextStatuses());
    }
}
