<?php

namespace App\Models\Versions\Queries;

use App\Http\Requests\Api\V1\Info\InfoController\ReleasesRequest;
use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class VersionQueryFactory
{
    public function createFromReleasesRequest(ReleasesRequest $request): Builder
    {
        $query = Version::query();

        $query->when($request->getType(), function (Builder $query, AppType $appType) {
            $query->where('app_type', '=', $appType);
        });

        $query->when($request->getAfter(), function (Builder $query, Carbon $after) {
            $query->where('created_at', '>', $after);
        });

        return $query;
    }
}
