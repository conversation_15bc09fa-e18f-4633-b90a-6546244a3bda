<?php

namespace App\Models\Infrastructure;

use App\Models\Installs\Install;
use App\Models\Versions\Version;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstanceVersion extends Model
{
    protected $guarded = ['id'];
    protected $table = 'instance_version';

    public function instance(): BelongsTo
    {
        return $this->belongsTo(Instance::class);
    }

    public function version(): BelongsTo
    {
        return $this->belongsTo(Version::class);
    }

    public function install(): BelongsTo
    {
        return $this->belongsTo(Install::class);
    }
}
