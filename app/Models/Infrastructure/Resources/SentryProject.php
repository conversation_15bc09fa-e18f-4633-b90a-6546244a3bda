<?php

namespace App\Models\Infrastructure\Resources;

use App\Models\Infrastructure\Resources\Casts\ClientKeyCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class SentryProject extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'client_key' => ClientKeyCast::class,
        ];
    }
}
