<?php

namespace App\Models\Infrastructure\Resources\Casts;

use App\System\Installs\Stages\Implementations\Sentry\Data\ClientKey;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;

class ClientKeyCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        if (empty($attributes['client_key'])) {
            return null;
        }

        $data = json_decode($attributes['client_key'], true);

        return ClientKey::createFromData($data);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (! $value instanceof ClientKey) {
            throw new InvalidArgumentException('The given value is not an ClientKey!');
        }

        return [
            'client_key' => json_encode($value->data),
        ];
    }
}
