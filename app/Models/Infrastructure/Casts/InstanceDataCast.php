<?php

namespace App\Models\Infrastructure\Casts;

use App\Models\Infrastructure\Instance;
use App\System\Infrastructure\Instances\Contracts\InstanceData;
use App\System\Infrastructure\Instances\Factories\InstanceDataFactory;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;
use LogicException;

class InstanceDataCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): InstanceData|null
    {
        if (! $model instanceof Instance) {
            throw new LogicException('InstanceData cast works only with Instances!');
        }

        if (empty($attributes[$key])) {
            return null;
        }

        $data = json_decode($attributes[$key], true);

        return (new InstanceDataFactory())->createForAppFromData($model->app_type, $data);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes): array
    {
        if (! $model instanceof Instance) {
            throw new LogicException('InstanceData cast works only with Instances!');
        }

        if (! $value instanceof InstanceData) {
            throw new InvalidArgumentException('The given value is not an implementation of InstanceData!');
        }

        return [
            $key => json_encode($value->getData()),
        ];
    }
}
