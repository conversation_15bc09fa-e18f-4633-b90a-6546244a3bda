<?php

namespace App\Models\Infrastructure\Connections;

use App\Models\Infrastructure\Instance;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class SshConnection extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'timeout' => 'integer',
            'remove_bash' => 'boolean',
        ];
    }

    public function instances(): MorphToMany
    {
        return $this->morphToMany(Instance::class, 'connection', 'instance_connection');
    }
}
