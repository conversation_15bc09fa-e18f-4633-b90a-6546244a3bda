<?php

namespace App\Models\Infrastructure;

use App\Models\Infrastructure\Casts\InstanceDataCast;
use App\Models\Infrastructure\Connections\SshConnection;
use App\System\Apps\Data\AppType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Instance extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'app_type' => AppType::class,
            'data' => InstanceDataCast::class,
        ];
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class)->withTrashed();
    }

    public function instanceVersions(): HasMany
    {
        return $this->hasMany(InstanceVersion::class);
    }

    public function sshConnections(): MorphToMany
    {
        return $this->morphedByMany(SshConnection::class, 'connection', 'instance_connection')->withTimestamps();
    }
}
