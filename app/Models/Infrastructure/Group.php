<?php

namespace App\Models\Infrastructure;

use App\Models\Installs\Install;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Group extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    public function instances(): HasMany
    {
        return $this->hasMany(Instance::class);
    }

    public function installs(): HasMany
    {
        return $this->hasMany(Install::class);
    }
}
