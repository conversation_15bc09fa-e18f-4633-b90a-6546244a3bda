<?php

namespace App\Models\Installs\Queries;

use App\Http\Requests\Api\V1\Installs\InstallController\ListRequest;
use App\Models\Installs\Install;
use App\System\Installs\Data\InstallStatus;
use Illuminate\Database\Eloquent\Builder;

class InstallQueryFactory
{
    public function createFromListRequest(ListRequest $request): Builder
    {
        $query = Install::query();

        $query->when($request->getStatus(), function (Builder $query, InstallStatus $status) {
            $query->currentStatus($status->value);
        });

        return $query;
    }
}
