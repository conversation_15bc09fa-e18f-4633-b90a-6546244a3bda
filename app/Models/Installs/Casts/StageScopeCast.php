<?php

namespace App\Models\Installs\Casts;

use App\System\Installs\Stages\Data\StageScope;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use InvalidArgumentException;

class StageScopeCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): StageScope|null
    {
        if (empty($attributes[$key])) {
            return null;
        }

        $data = json_decode($attributes[$key], true);

        return new StageScope(
            $data['main'],
            Arr::get($data, 'refs', []),
        );
    }

    public function set(Model $model, string $key, mixed $value, array $attributes): array
    {
        if (! $value instanceof StageScope) {
            throw new InvalidArgumentException('The given value is not an StageScope!');
        }

        $data = ['main' => $value->main];
        if (! empty($value->refs)) {
            $data['refs'] = $value->refs;
        }

        return [
            $key => json_encode($data),
        ];
    }
}
