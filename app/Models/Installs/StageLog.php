<?php

namespace App\Models\Installs;

use App\System\Installs\Stages\Data\StageLogType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StageLog extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'type' => StageLogType::class,
        ];
    }

    public function stage(): BelongsTo
    {
        return $this->belongsTo(Stage::class)->withTrashed();
    }
}
