<?php

namespace App\Models\Installs;

use App\Models\Infrastructure\Group;
use App\Models\Traits\HasStatuses;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Data\RunType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use LogicException;

class Install extends Model
{
    use HasStatuses;
    use HasFactory;
    use SoftDeletes;

    protected $statusEnumClass = InstallStatus::class;
    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'payload' => 'array',
            'type' => InstallType::class,
            'run_type' => RunType::class,
            'resulting_context' => 'array',
            'result' => 'array',

            'ended_at' => 'datetime',
        ];
    }

    public function stages(): HasMany
    {
        return $this->hasMany(Stage::class)->orderBy('order', 'asc');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function getInstallPayload(): InstallPayload
    {
        return new InstallPayload($this->payload, $this->group);
    }

    public function setInstallPayload(InstallPayload $payload): static
    {
        $this->payload = $payload->data;
        $this->group()->associate($payload->group);

        return $this;
    }

    public function endWithStatus(InstallStatus $status)
    {
        if (! $status->isFinal()) {
            throw new LogicException('Cannot end install with non-final status!');
        }

        $this->ended_at = now();
        $this->setStatus($status);

        $this->save();
    }
}
