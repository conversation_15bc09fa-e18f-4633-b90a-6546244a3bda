<?php

namespace App\Models\Installs;

use App\Models\Installs\Casts\StageScopeCast;
use App\Models\Traits\HasStatuses;
use App\System\Installs\Stages\Data\StageStatus;
use App\System\Installs\Stages\Implementations\Data\StageImplementationMethod;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use LogicException;

class Stage extends Model
{
    use HasStatuses;
    use HasFactory;
    use SoftDeletes;

    protected $statusEnumClass = StageStatus::class;
    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'scope' => StageScopeCast::class,
            'implementation_method' => StageImplementationMethod::class,

            'prior_context' => 'array',
            'resulting_context' => 'array',

            'ended_at' => 'datetime',
        ];
    }

    public function install(): BelongsTo
    {
        return $this->belongsTo(Install::class);
    }

    public function logs(): HasMany
    {
        return $this->hasMany(StageLog::class);
    }

    public function endWithStatus(StageStatus $status)
    {
        if (! $status->isFinal()) {
            throw new LogicException('Cannot end stage with non-final status!');
        }

        $this->ended_at = now();
        $this->setStatus($status);

        $this->save();
    }
}
