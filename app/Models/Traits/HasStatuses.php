<?php

namespace App\Models\Traits;

use Spatie\ModelStatus\HasStatuses as SpatieHasStatuses;

trait HasStatuses
{
    use SpatieHasStatuses {
        SpatieHasStatuses::setStatus as private spatieSetStatus;
        SpatieHasStatuses::forceSetStatus as private spatieForceSetStatus;
        SpatieHasStatuses::__get as private spatie__get;
    }

    public function forceSetStatus($status, ?string $reason = null): self
    {
        if (property_exists($this, 'statusEnumClass')) {
            $name = $status instanceof $this->statusEnumClass
                ? $status->value
                : $status;
        }

        return $this->spatieForceSetStatus($name, $reason);
    }

    public function setStatus($status, ?string $reason = null): self
    {
        if (property_exists($this, 'statusEnumClass')) {
            if (! $status instanceof $this->statusEnumClass) {
                $status = ($this->statusEnumClass)::from($status);
            }

            return $this->spatieSetStatus($status->value, $reason);
        }

        return $this->spatieSetStatus((string) $status, $reason);
    }

    public function __get($key): mixed
    {
        if ($key === $this->getStatusAttributeName()) {
            if (property_exists($this, 'statusEnumClass')) {
                return ($this->statusEnumClass)::from((string) $this->latestStatus());
            }
        }

        return $this->spatie__get($key);
    }
}
