<?php

namespace App\Listeners\Http;

use App\Models\Logs\ExternalRequestLog;
use Illuminate\Http\Client\Events\RequestSending;

class LogSendingRequest
{
    public function __construct()
    {
        //
    }

    public function handle(RequestSending $event): void
    {
        $record = new ExternalRequestLog();
        $record->fill([
            'request_method' => $event->request->method(),
            'request_headers' => $event->request->headers(),
            'request_body' => $event->request->body(),
            'request_url' => $event->request->url(),
        ]);
        $record->tryAssigningModelFromApiGuard(request());
        $record->save();

        app()->instance('external-request-log', $record);
    }
}
