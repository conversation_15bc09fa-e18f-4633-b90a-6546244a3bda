<?php

namespace App\Listeners\Http;

use App\Models\Logs\ExternalRequestLog;
use Illuminate\Http\Client\Events\ResponseReceived;

class LogReceivingResponse
{
    public function __construct()
    {
        //
    }

    public function handle(ResponseReceived $event): void
    {
        $record = app()->bound('external-request-log')
            ? app('external-request-log')
            : new ExternalRequestLog();

        $record->fill([
            'request_method' => $event->request->method(),
            'request_headers' => $event->request->headers(),
            'request_body' => json_decode($event->request->body(), true),
            'request_url' => $event->request->url(),

            'response_body' => json_decode($event->response->body(), true),
            'response_headers' => $event->response->headers(),
            'response_http_code' => $event->response->status(),
        ]);
        $record->tryAssigningModelFromApiGuard(request());
        $record->save();

        app()->forgetInstance('external-request-log');
    }
}
