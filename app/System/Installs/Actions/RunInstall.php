<?php

namespace App\System\Installs\Actions;

use App\Models\Installs\Install;
use App\Models\Installs\Stage;
use App\System\Installs\Actions\Exceptions\InstallStatusException;
use App\System\Installs\Actions\Exceptions\NoContextException;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\RunType;
use App\System\Installs\Stages\Exceptions\StageStatusException;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageStatus;
use App\System\Installs\Stages\Factories\StagesStrategyFactory;
use App\System\Installs\Stages\Implementations\Data\StageImplementationMethod;
use App\System\Runtime\Context;
use LogicException;
use Throwable;

class RunInstall
{
    public function __construct(private Install $install)
    {
        //
    }

    public function execute(): void
    {
        if ($this->install->status !== InstallStatus::PENDING) {
            throw new InstallStatusException($this->install, InstallStatus::PENDING);
        }

        app()->instance('install.ongoing', $this->install);

        $stages = $this->install->stages;
        $previous = null;
        $context = null;

        $runningStage = $stages->first(fn ($stage) => $stage->status === StageStatus::RUNNING);
        if (! empty($runningStage)) {
            throw new StageStatusException($runningStage, StageStatus::PENDING);
        }

        $queue = $stages->values()->all();
        $successfullyRun = collect();
        $rollbackMode = false;
        $hadErrors = false;
        while (true) {
            if (empty($queue)) {
                $this->install->endWithStatus(
                    $hadErrors ? InstallStatus::FAILED : InstallStatus::FINISHED,
                );
                $this->install->resulting_context = $context?->toArray();

                /**
                 * Forming and saving result for this install.
                 *
                 * @var \App\System\Installs\Stages\Contracts\StagesStrategy $strategy
                 */
                $strategy = app(StagesStrategyFactory::class)->createForInstall($this->install);

                // Only fill in result if we had no errors.
                if (! $hadErrors) {
                    $this->install->result = $strategy->getResult($context);
                }

                $this->install->group()->associate($strategy->getGroup($context));
                $this->install->save();

                break;
            }

            $stage = array_shift($queue);
            if (! $stage) {
                continue;
            }

            // Going through only with stages that are pending for execution.
            if ($stage->status === StageStatus::PENDING) {
                $context = $this->resolveContext(existing: $context, currentStage: $stage, previousStage: $previous);

                $implementation = $this->createImplementation($stage, $context);

                try {
                    $stage->setStatus(StageStatus::RUNNING);

                    // 1. Saving context prior to executing the stage implementation.
                    $stage->prior_context = $context->toArray();
                    $stage->save();

                    // 2. IMPORTANT! Running stage implementation.
                    $stage->implementation_method->apply($implementation);

                    // 3. Saving resulting context after execution.
                    $stage->resulting_context = $context->toArray();
                    $stage->save();

                    $stage->endWithStatus(StageStatus::FINISHED);

                    $successfullyRun->push($stage);
                } catch (Throwable $e) {
                    $hadErrors = true;
                    $stage->endWithStatus(StageStatus::FAILED);

                    if ($this->install->run_type === RunType::IGNORE_ERRORS_NO_ROLLBACKS) {
                        report($e);

                        continue;
                    }

                    if ($this->install->run_type === RunType::ROLLBACK_ON_FIRST_ERROR) {
                        foreach ($queue as $aheadStage) {
                            if ($aheadStage->status === StageStatus::PENDING) {
                                $aheadStage->endWithStatus(StageStatus::CANCELED);
                            }
                        }

                        // If we are still not in rollback mode and failed stage was not rolling back then
                        // we should add new stages for rolling back successfully run ones.
                        if (! $rollbackMode && $stage->implementation_method === StageImplementationMethod::RUN) {
                            $rollbackMode = true;

                            $newOrder = $stage->order + 1; // inserting rolling back stages right after failed one.

                            // First we queue rollback for failed stage (so it can clear after itself).
                            $queue[] = $this->createRollbackStageFrom($stage, $newOrder);
                            $newOrder++;

                            // Then we queue all previously successfully run stages in reverse order.
                            foreach ($successfullyRun->reverse() as $needsRollingBack) {
                                $rollbackStage = $this->createRollbackStageFrom($needsRollingBack, $newOrder);
                                $newOrder++;

                                $queue[] = $rollbackStage;
                            }

                            // Lastly we need to move cancelled stages in order after the rollback ones.
                            $this->install->load('stages');
                            foreach ($this->install->stages as $updatedStage) {
                                if ($updatedStage->status === StageStatus::CANCELED) {
                                    $updatedStage->order = $newOrder;
                                    $updatedStage->save();

                                    $newOrder++;
                                }
                            }
                        }

                        report($e);

                        continue;
                    }

                    throw new LogicException(sprintf(
                        'No implementation found for %s run type!',
                        $this->install->run_type->value,
                    ));
                } finally {
                    // Clearing up after stage implementation in case it registered any command hooks, but failed
                    // to clear them up itself.
                    app('command.hub')->clearCommandHooks();
                }
            }

            $previous = $stage;
        }

        app()->forgetInstance('install.ongoing');
    }

    private function resolveContext(Context|null $existing, Stage $currentStage, Stage|null $previousStage): Context
    {
        // If we are missing context then we try to get it from current/previous stages.
        if ($existing === null) {
            if ($currentStage->prior_context) {
                $contextData = $currentStage->prior_context;
            } else {
                if ($previousStage) {
                    $contextData = $previousStage->resulting_context;
                }
            }

            if (! isset($contextData)) {
                throw new NoContextException($currentStage);
            }

            $existing = new Context($contextData);
        }

        return $existing;
    }

    private function createImplementation(Stage $stage, Context $context): StageImplementation
    {
        /** @var \App\System\Installs\Stages\Contracts\StageImplementation */
        $implementation = app($stage->implementation);

        $implementation->linkWithStage($stage);
        if ($stage->scope) {
            $implementation->provideScope($stage->scope);
        }

        $implementation->provideContext($context);
        $implementation->provideName($stage->name);

        return $implementation;
    }

    private function createRollbackStageFrom(Stage $needsRollingBack, int $newOrder): Stage
    {
        $rollbackStage = new Stage();
        $rollbackStage->install()->associate($this->install);

        $rollbackStage->name = sprintf('Rollback "%s"', $needsRollingBack->name);
        $rollbackStage->implementation = $needsRollingBack->implementation;
        $rollbackStage->implementation_method = StageImplementationMethod::ROLLBACK;
        $rollbackStage->scope = $needsRollingBack->scope;
        $rollbackStage->order = $newOrder;

        $rollbackStage->save();
        $rollbackStage->setStatus(StageStatus::PENDING);

        return $rollbackStage;
    }
}
