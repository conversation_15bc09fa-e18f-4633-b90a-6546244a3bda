<?php

namespace App\System\Installs\Actions\Exceptions;

use App\Models\Installs\Install;
use App\System\Installs\Data\InstallStatus;
use Exception;

class InstallStatusException extends Exception
{
    public function __construct(
        public readonly Install $install,
        public readonly InstallStatus $mustBe,
    ) {
        parent::__construct(sprintf(
            'Install #%d status must be "%s", but is "%s" instead!',
            $install->id,
            $mustBe->value,
            $install->status,
        ));
    }
}
