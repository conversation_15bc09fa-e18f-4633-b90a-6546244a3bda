<?php

namespace App\System\Installs\Actions;

use App\Models\Installs\Install;
use App\Models\Installs\Stage;
use App\System\Installs\Actions\Data\StartInstallResult;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Data\RunType;
use App\System\Installs\Stages\Data\StageStatus;
use App\System\Installs\Stages\Factories\StagesStrategyFactory;
use App\System\Installs\Stages\Implementations\Data\StageImplementationMethod;
use Illuminate\Support\Facades\DB;

class StartInstall
{
    private InstallType $type;
    private RunType $runType = RunType::ROLLBACK_ON_FIRST_ERROR;
    private InstallPayload $payload;

    public function __construct()
    {
        //
    }

    public function provideRunType(RunType $runType): static
    {
        $this->runType = $runType;

        return $this;
    }

    public function provideType(InstallType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function providePayload(InstallPayload $payload): static
    {
        $this->payload = $payload;

        return $this;
    }

    public function execute(): StartInstallResult
    {
        return DB::transaction(function () {
            $install = new Install();

            $install->type = $this->type;
            $install->run_type = $this->runType;
            $install->setInstallPayload($this->payload);
            $install->save();
            $install->setStatus(InstallStatus::PENDING->value);

            $this->setupStages($install);

            return new StartInstallResult($install);
        });
    }

    private function setupStages(Install $install): void
    {
        /** @var \App\System\Installs\Stages\Contracts\StagesStrategy */
        $strategy = app(StagesStrategyFactory::class)->createForInstall($install);

        $stages = $strategy->getStages();

        $order = 0;
        foreach ($stages->items as $stage) {
            $stageRecord = new Stage();

            $stageRecord->install()->associate($install);

            $stageRecord->name = $stage->getName();
            $stageRecord->implementation = $stage->getClass();
            $stageRecord->implementation_method = StageImplementationMethod::RUN;
            $stageRecord->scope = $stage->getScope();
            $stageRecord->order = $order;

            // Saving initial context state for the first stage.
            if ($order === 0) {
                $stageRecord->prior_context = $stages->context->toArray();
            }

            $stageRecord->save();
            $stageRecord->setStatus(StageStatus::PENDING);

            $stage->linkWithStage($stageRecord);
            $order++;
        }
    }
}
