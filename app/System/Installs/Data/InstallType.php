<?php

namespace App\System\Installs\Data;

use App\System\Apps\Data\AppType;
use LogicException;

enum InstallType: string
{
    case BASIC_DMS_SETUP = 'basic_dms_setup';
    case UPGRADE_DMS = 'upgrade_dms';
    case UPGRADE_PWA = 'upgrade_pwa';
    case PWA_FOR_DMS_SETUP = 'pwa_for_dms_setup';
    case DELETE_DMS_INSTANCE = 'delete_dms_instance';
	case UPGRADE_DMS_SUBSCRIPTION = 'upgrade_dms_subscription';
	case UPGRADE_DMS_INSTANCES_LIMITS_STATS = 'upgrade_dms_instances_limits_stats';
    case ADD_ENVELOPE_TO_OLD_DMS = 'add_envelope_to_old_dms';

    public function for(): AppType
    {
        switch ($this) {
            case self::BASIC_DMS_SETUP:
            case self::UPGRADE_DMS:
            case self::DELETE_DMS_INSTANCE:
			case self::UPGRADE_DMS_SUBSCRIPTION:
			case self::UPGRADE_DMS_INSTANCES_LIMITS_STATS:
            case self::ADD_ENVELOPE_TO_OLD_DMS:
                return AppType::DMS;
                // no break
            case self::PWA_FOR_DMS_SETUP:
            case self::UPGRADE_PWA:
                return AppType::PWA;
        }

        throw new LogicException(sprintf('Install type "%s" is unaccounted for!', $this->value));
    }
}
