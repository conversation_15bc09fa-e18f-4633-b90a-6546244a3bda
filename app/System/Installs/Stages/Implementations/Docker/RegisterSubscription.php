<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use Throwable;
use Symfony\Component\Process\Process;

class RegisterSubscription implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

	private string $tempFile;
	private string $remoteTempPath = '/tmp/subscription.json';
	private string $containerPath = '/tmp/subscription.json';

    public function __construct()
    {
        $this->tempFile = $this->generateTemporaryJsonFile();
    }

    /**
     * @throws Throwable
     */
    public function run(): void
    {
        $this->logCommandResultsToStage();

		$this->log(StageLogType::INFO, 'Generating subscription data JSON...');
		$this->log(StageLogType::INFO, 'Local temp file: ' . $this->tempFile);
		$this->storeSubscriptionDataIntoFile();

		if (!file_exists($this->tempFile)) {
			$this->log(StageLogType::ERROR, 'Temp file NOT found after writing!');
			throw new \RuntimeException("Temp file does not exist: {$this->tempFile}");
		}

		$this->log(StageLogType::INFO, 'Transferring JSON file to remote server...');
		$this->copyFileToRemoteServer();

		$this->log(StageLogType::INFO, 'Initializing SSH connection...');
		$this->try(function () {
			$this->initializeSshExec(
				$this->get('host'),
				$this->get('user'),
				$this->get('private_key'),
			);
		});

		$this->moveFileFromRemoteToContainer();
		$this->importSubscriptionFromFileToDb();
	}

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }

    private function generateTemporaryJsonFile(): string
    {
        return tempnam(sys_get_temp_dir(), 'subscription_data_') . '.json';
    }

	private function storeSubscriptionDataIntoFile(): void
	{
		$jsonContent = json_encode([
			'subscription' => $this->get('subscription'),
			'system_features_stats' => $this->get('system_features_stats'),
			'service_features' => $this->get('service_features'),
		], JSON_PRETTY_PRINT);

		file_put_contents($this->tempFile, $jsonContent);
	}

	private function copyFileToRemoteServer(): void
	{
		$privateKeyPath = $this->get('private_key');
		$user = $this->get('user');
		$host = $this->get('host');

		$scpCommand = [
			'scp',
			'-i', $privateKeyPath,
			'-o', 'StrictHostKeyChecking=no',
			$this->tempFile,
			"{$user}@{$host}:{$this->remoteTempPath}"
		];

		$this->log(StageLogType::INFO, 'Running SCP: ' . implode(' ', $scpCommand));
		$process = new Process($scpCommand);
		$process->run();

		if (!$process->isSuccessful()) {
			throw new \RuntimeException("Failed to copy file to remote server: " . $process->getErrorOutput());
		}
	}

	/**
	 * @throws Throwable
	 */
	private function moveFileFromRemoteToContainer(): void
	{
		$this->log(StageLogType::INFO, 'Copying JSON file from remote host to DMS container...');

		$this->try(function () {
			$dockerCpCommand = new SshCommand(sprintf(
				'docker cp %s dms-dmsphpfpm-1:%s',
				escapeshellarg($this->remoteTempPath),
				escapeshellarg($this->containerPath),
			));

			app('command.hub')->execute($dockerCpCommand, $this->sshExec);
		});
	}

	/**
	 * @throws Throwable
	 */
	public function importSubscriptionFromFileToDb(): void
	{
		$mode = $this->get('mode') ?? 'create';

		$this->log(StageLogType::INFO, 'Importing subscription into DMS from container file...');
		$this->try(function () use ($mode) {
			$importCommand = new SshCommand(sprintf(
				'docker exec -t --user www-data dms-dmsphpfpm-1 sh -c %s',
				escapeshellarg(
					sprintf('php artisan subscription:import-from-file %s --mode=%s', escapeshellarg($this->containerPath), $mode)
				),
			));

			return app('command.hub')->execute($importCommand, $this->sshExec);
		});
	}
}
