<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Filesystem\Support\CopyFile;
use App\System\Installs\Stages\Implementations\Filesystem\Support\ReplaceEnvValues;
use App\System\Installs\Stages\Implementations\Filesystem\Support\WriteToFile;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;

class UpdateDockerComposeConfigs implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating provided instance...');
        $instance = Instance::findOrFail($this->get('instance_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        $this->log(StageLogType::INFO, 'Waiting until SSH connection is available...');
        $this->waitUntilSshConnectionIsAvailable();

        $this->log(StageLogType::INFO, 'Creating docker-compose.yaml file...');
        $this->try(function () {
            app(WriteToFile::class)
                ->provideSshExec($this->sshExec)
                ->write(
                    $this->get('yaml_file'),
                    $this->get('yaml_contents'),
                    append: false, // replacing file contents entirely.
                );
        });

        $this->log(StageLogType::INFO, 'Backing up existing docker compose env file');
        $this->try(function () {
            app(CopyFile::class)
                ->provideSshExec($this->sshExec)
                ->copy($this->get('env_file'), sprintf('backup_%s_%s', (string) time(), $this->get('env_file')));
        });

        $this->log(StageLogType::INFO, 'Change tag in docker compose env file...');
        $this->try(function () {
            app(ReplaceEnvValues::class)
                ->provideSshExec($this->sshExec)
                ->replace($this->get('env_file'), [
                    'TAG' => $this->get('tag'),
                ], escape: false);
        });
    }

    public function rollback(): void
    {
        // TODO consider reading and saving original content?

        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
