<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Docker\Support\IssueUpCommand;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;

class DockerComposeStart implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating provided instance...');
        $instance = Instance::findOrFail($this->get('instance_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        $this->log(StageLogType::INFO, 'Waiting until SSH connection is available...');
        $this->waitUntilSshConnectionIsAvailable();

        $this->log(StageLogType::INFO, 'Start containers...');
        $this->try(function () {
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            app(IssueUpCommand::class)
                ->provideSshExec($this->sshExec)
                ->execute($this->get('env_file'), $this->get('yaml_file'), true);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
