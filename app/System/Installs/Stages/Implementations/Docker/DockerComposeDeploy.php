<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use RuntimeException;

class DockerComposeDeploy implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () {
            $this->initializeSshExec(
                $this->get('host'),
                $this->get('user'),
                $this->get('private_key'),
            );
        });

        $this->log(StageLogType::INFO, 'Waiting until SSH connection is available...');
        $this->waitUntilSshConnectionIsAvailable();

        $this->log(StageLogType::INFO, 'Establishing Docker Registry login...');
        $this->try(function () {
            [$registryUser, $registryPassword, $registry] = $this->getRegistryAttributesFromContext();
            $dockerLoginCommand = new SshCommand(sprintf(
                'docker login -u %s -p %s %s',
                $registryUser,
                $registryPassword,
                $registry,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($dockerLoginCommand, $this->sshExec);
        });

        $prefixComposeFiles = $this->get('prefix_compose_files', false, true);
        if ($prefixComposeFiles) {
            $composeProjectName = $this->get('compose_project_name');

            $env = sprintf('%s-docker-compose.env', $composeProjectName);
            $yaml = sprintf('%s-docker-compose.yaml', $composeProjectName);
            $build = sprintf('%s-build-artifact.yaml', $composeProjectName);
        } else {
            $env = 'docker-compose.env';
            $yaml = 'docker-compose.yaml';
            $build = 'build-artifact.yaml';
        }

        $networks = $this->get('docker_networks', false, []);
        foreach ($networks as $networkName) {
            $this->log(StageLogType::INFO, sprintf('Creating network "%s"...', $networkName));
            $this->try(function () use ($networkName) {
                $createNetworkCommand = new SshCommand(sprintf(
                    'docker network create %s',
                    $networkName,
                ));

                /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
                return app('command.hub')->execute($createNetworkCommand, $this->sshExec);
            });
        }

        $this->log(StageLogType::INFO, 'Creating .env file...');
        $this->try(function () use ($env) {
            $createEnvCommand = new SshCommand(sprintf(
                "echo -e '%s' > %s",
                $this->getEnvFileContent(),
                $env,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($createEnvCommand, $this->sshExec);
        });

        $this->log(StageLogType::INFO, 'Creating docker-compose.yaml file...');
        $this->try(function () use ($yaml) {
            $createDockerComposeYamlCommand = new SshCommand(sprintf(
                "echo -e '%s' > %s",
                $this->getDockerComposeYamlFileContent(),
                $yaml,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($createDockerComposeYamlCommand, $this->sshExec);
        });

        $this->log(StageLogType::INFO, 'Getting docker compose config...');
        $config = $this->try(function () use ($env, $yaml, $build) {
            $configureDockerComposeCommand = new SshCommand(sprintf(
                'docker compose --env-file %s -f %s config > ./%s',
                $env,
                $yaml,
                $build,
            ));
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            app('command.hub')->execute($configureDockerComposeCommand, $this->sshExec);

            $readBuildArtifact = new SshCommand(sprintf('cat %s', $build));
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $configResult = app('command.hub')->execute($readBuildArtifact, $this->sshExec);

            if (empty($configResult->payload['stdout'])) {
                throw new RuntimeException('No output from docker compose config command! Something went wrong.');
            }

            return $configResult->payload['stdout'];
        });

        $this->set('docker_compose_config', $config);
        $this->log(StageLogType::INFO, 'Docker compose configuration successfully saved!');

        $this->log(StageLogType::INFO, 'Starting docker compose...');
        $this->try(function () use ($env, $yaml) {
            $startDockerCompose = new SshCommand(sprintf(
                'docker compose --env-file %s -f %s up -d --force-recreate',
                $env,
                $yaml,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($startDockerCompose, $this->sshExec);
        });

        sleep(2);
    }

    public function rollback(): void
    {
        // TODO maybe stop all containers to prevent constant restart?

        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }

    private function getRegistryAttributesFromContext(): array
    {
        $user = $this->get('registry_user');
        $password = $this->get('registry_password');
        $registry = $this->get('registry');

        if (! $user || ! $password || ! $registry) {
            throw new RuntimeException('Context missing required docker registry attributes!');
        }

        return [$user, $password, $registry];
    }

    private function getDockerComposeYamlFileContent(): string
    {
        $class = $this->get('yaml_template_generator');

        /** @var \App\System\Installs\Stages\Contracts\TemplateGenerator */
        $generator = app($class);

        return $generator->generate($this);
    }

    private function getEnvFileContent(): string
    {
        $class = $this->get('env_template_generator');

        /** @var \App\System\Installs\Stages\Contracts\TemplateGenerator */
        $generator = app($class);

        return $generator->generate($this);
    }
}
