<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\SshCommandExec;
use RuntimeException;

class RegisterCheckerCredentials implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $sshExec = $this->try(function () {
            $host = $this->get('host');
            $user = $this->get('user');
            $privateKey = $this->get('private_key');

            return app(SshCommandExec::class, ['host' => $host, 'user' => $user, 'privateKey' => $privateKey]);
        });

        $this->log(StageLogType::INFO, 'Generating new Laravel Passport grant client...');
        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $passportClient = $this->try(function () use ($sshExec) {
            $containerName = $this->get('container_name');
            $name = $this->get('app_name');
            $generateClient = new SshCommand(sprintf(
                'docker exec %s php artisan passport:client --client --name="%s Grant Client"',
                $containerName,
                $name,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($generateClient, $sshExec);
        });

        $this->log(StageLogType::INFO, 'Parsing client credentials from command output...');
        [$clientId, $clientSecret] = $this->try(function () use ($passportClient) {
            $output = $passportClient->payload['stdout'] ?? '';
            $search = preg_match('/^.+Client ID: (?<id>\d+).*Client secret: (?<secret>[^\n]+)$/s', $output, $matches);
            if (! $search) {
                throw new RuntimeException('Could not find Client ID and Client Secret data in provided stdout!');
            }

            ['id' => $clientId, 'secret' => $clientSecret] = $matches;

            return [$clientId, $clientSecret];
        });

        $this->log(StageLogType::INFO, sprintf(
            'Successfully parsed values client_id=%s, client_secret=%s',
            $clientId,
            $clientSecret,
        ));
        $this->set('client_id', $clientId);
        $this->set('client_secret', $clientSecret);
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
