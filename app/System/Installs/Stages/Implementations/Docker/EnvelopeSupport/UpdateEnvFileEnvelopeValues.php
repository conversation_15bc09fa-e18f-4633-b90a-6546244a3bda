<?php

namespace App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport;

use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Filesystem\Support\CopyFile;
use App\System\Installs\Stages\Implementations\Filesystem\Support\ReplaceEnvValues;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;

class UpdateEnvFileEnvelopeValues implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating instance...');
        $instance = Instance::findOrFail($this->get('instance_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH connection...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        $envFile = $this->get('env_file');
        $envValues = [
            'ENVELOPE_BASE_URL' => $this->get('ENVELOPE_BASE_URL'),
            'ENVELOPE_APP_ID' => $this->get('ENVELOPE_APP_ID'),
            'ENVELOPE_AUTH_KEY_ID' => $this->get('ENVELOPE_AUTH_KEY_ID'),
            'ENVELOPE_AUTH_KEY_SECRET' => $this->get('ENVELOPE_AUTH_KEY_SECRET'),
        ];

        $this->log(StageLogType::INFO, "Backing up environment file: $envFile");
        $this->try(function () use ($envFile) {
            app(CopyFile::class)
                ->provideSshExec($this->sshExec)
                ->copy($envFile, sprintf('backup_%s_%s', time(), $envFile));
        });

        $this->log(StageLogType::INFO, 'Updating .env values...');
        $this->try(function () use ($envFile, $envValues) {
            app(ReplaceEnvValues::class)
                ->provideSshExec($this->sshExec)
                ->replace($envFile, $envValues, escape: false);
        });

        $this->log(StageLogType::INFO, 'Environment file updated successfully.');
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not implemented.');
    }
}
