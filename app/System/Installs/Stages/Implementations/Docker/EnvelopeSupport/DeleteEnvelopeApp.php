<?php

namespace App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\SshCommandExec;

class DeleteEnvelopeApp implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');

        $sshExec = $this->createSshExec();

        $this->log(StageLogType::INFO, 'Deleting envelope application...');

        $this->try(function () use ($sshExec) {
            $containerName = $this->get('envelope_for_dms.container_name');
            $appId = $this->get('envelope_for_dms.app_id', required: false);

            if (empty($appId)) {
                $this->log(StageLogType::INFO, 'No registered Envelope app found, nothing to do.');

                return;
            }

            $deleteCommand = new SshCommand(sprintf(
                'docker exec %s php artisan app:delete-keys "%s"',
                $containerName,
                $appId
            ));

            app('command.hub')->execute($deleteCommand, $sshExec);

            $this->log(StageLogType::INFO, sprintf('Successfully deleted application with ID: %s', $appId));
        });
    }

    protected function createSshExec(): SshCommandExec
    {
        return $this->try(function () {
            return app(SshCommandExec::class, [
                'host' => $this->get('envelope_for_dms.host'),
                'user' => $this->get('envelope_for_dms.user'),
                'privateKey' => $this->get('envelope_for_dms.private_key'),
            ]);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
