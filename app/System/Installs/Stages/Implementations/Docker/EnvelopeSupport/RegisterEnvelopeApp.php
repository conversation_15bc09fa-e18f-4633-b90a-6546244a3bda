<?php

namespace App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\SshCommandExec;
use RuntimeException;

class RegisterEnvelopeApp implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');

        $sshExec = $this->createSshExec();

        $this->log(StageLogType::INFO, 'Generating new application ...');

        $applicationData = $this->try(function () use ($sshExec) {
            $containerName = $this->get('container_name');
            $name = $this->get('app_name');
            $appUrl = $this->get('app_url');
            $webhookUrl = $this->get('webhook_url');

            $generateApplication = new SshCommand(sprintf(
                'docker exec %s php artisan app:create-keys "%s" "%s" "%s"',
                $containerName,
                $name,
                $appUrl,
                $webhookUrl
            ));

            /** @var CommandExecResult */
            return app('command.hub')->execute($generateApplication, $sshExec);
        });

        $this->log(StageLogType::INFO, 'Parsing credentials from command output...');
        [$appId, $authKeyId, $authKeySecret] = $this->try(function () use ($applicationData) {
            $output = $applicationData->payload['stdout'] ?? '';

            $json = json_decode($output, true);

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($json)) {
                throw new RuntimeException('Invalid JSON received from stdout!');
            }

            if (!isset($json['app_id'], $json['client_id'], $json['client_secret'])) {
                throw new RuntimeException('Missing required keys in parsed JSON output!');
            }

            return [
                $json['app_id'],
                $json['client_id'],
                $json['client_secret'],
            ];
        });


        $this->log(StageLogType::INFO, sprintf(
            'Successfully parsed values app_id=%s, auth_key_id=%s, auth_key_secret=%s',
            $appId,
            $authKeyId,
            $authKeySecret
        ));

        $this->set('app_id', $appId);
        $this->set('auth_key_id', $authKeyId);
        $this->set('auth_key_secret', $authKeySecret);
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::WARNING, 'Initializing SSH client for rollback...');

        $sshExec = $this->createSshExec();

        $this->log(StageLogType::WARNING, 'Attempting to rollback: deleting created application...');

        $this->try(function () use ($sshExec) {
            $containerName = $this->get('container_name');

            $appId = $this->get('app_id', required: false);

            if (empty($appId)) {
                $this->log(StageLogType::INFO, 'No registered Envelope app found, nothing to do.');
                return;
            }

            $deleteCommand = new SshCommand(sprintf(
                'docker exec %s php artisan app:delete-keys "%s"',
                $containerName,
                $appId
            ));

            app('command.hub')->execute($deleteCommand, $sshExec);

            $this->log(StageLogType::WARNING, sprintf('Successfully rolled back application with ID: %s', $appId));
        });
    }

    protected function createSshExec(): SshCommandExec
    {
        return $this->try(function () {
            return app(SshCommandExec::class, [
                'host' => $this->get('host'),
                'user' => $this->get('user'),
                'privateKey' => $this->get('private_key'),
            ]);
        });
    }


}
