<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use Throwable;

class UpdateDmsLimitsStats implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    /**
     * @throws Throwable
     */
    public function run(): void
    {
        $this->logCommandResultsToStage();

		$this->log(StageLogType::INFO, 'Initializing SSH connection...');
		$this->try(function () {
			$this->initializeSshExec(
				$this->get('host'),
				$this->get('user'),
				$this->get('private_key'),
			);
		});

		$this->runSendLimitsStatsToScrmCommand();
	}

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }

	/**
	 * @throws Throwable
	 */
	public function runSendLimitsStatsToScrmCommand(): void
	{
		$this->log(StageLogType::INFO, 'Running send limits stats to scrm command...');
		$this->try(function () {
			$importCommand = new SshCommand(sprintf(
				'docker exec -t --user www-data dms-dmsphpfpm-1 sh -c %s',
				escapeshellarg('php artisan system-features:update-scrm'),
			));

			return app('command.hub')->execute($importCommand, $this->sshExec);
		});
	}
}
