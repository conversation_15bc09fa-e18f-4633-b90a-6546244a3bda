<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\SshCommandExec;
use RuntimeException;

class RegisterAiServiceCredentials implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');

        $sshExec = $this->try(function () {
            $host = $this->get('host');
            $user = $this->get('user');
            $privateKey = $this->get('private_key');

            return app(SshCommandExec::class, ['host' => $host, 'user' => $user, 'privateKey' => $privateKey]);
        });

        $this->log(StageLogType::INFO, 'Generating new token...');

        $apiToken = $this->try(function () use ($sshExec) {
            $containerName = $this->get('container_name');
            $name = $this->get('app_name');
            $generateClient = new SshCommand(sprintf(
                'docker exec %s php artisan api:generate-token "%s Client"',
                $containerName,
                $name,
            ));
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($generateClient, $sshExec);
        });

        $this->log(StageLogType::INFO, 'Parsing credentials from command output...');
        $token = $this->try(function () use ($apiToken) {
            $output = $apiToken->payload['stdout'] ?? '';

            // Match the line that contains the token using a simple regex
            $search = preg_match('/^Token:\s*(.+)$/m', $output, $matches);
            if (! $search) {
                throw new RuntimeException('Could not find API token in provided stdout!');
            }

            return trim($matches[1]);
        });

        $this->log(StageLogType::INFO, sprintf(
            'Successfully parsed API token: %s',
            $token
        ));

        $this->set('api_token', $token);
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}