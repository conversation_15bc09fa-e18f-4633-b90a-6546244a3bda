<?php

namespace App\System\Installs\Stages\Implementations\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\SshCommandExec;
use RuntimeException;

class RegisterScrmCredentials implements StageImplementation, Contextable
{
	use InteractsWithStage;
	use InteractsWithContext;

	public function run(): void
	{
		$this->logCommandResultsToStage();

		$this->log(StageLogType::INFO, 'Initializing SSH client...');
		$sshExec = $this->try(function () {
			$host = $this->get('host');
			$user = $this->get('user');
			$privateKey = $this->get('private_key');

			return app(SshCommandExec::class, ['host' => $host, 'user' => $user, 'privateKey' => $privateKey]);
		});

		$this->log(StageLogType::INFO, 'Generating new Laravel Passport grant client...');
		/** @var \App\System\Runtime\Commands\Data\CommandExecResult */
		$passportClient = $this->try(function () use ($sshExec) {
			$containerName = $this->get('container_name');
			$name = $this->get('app_name');

			$generateClient = new SshCommand(sprintf(
				'docker exec %s php artisan passport:client --client --name="%s Grant Client"',
				$containerName,
				$name
			));

			/** @var \App\System\Runtime\Commands\Data\CommandExecResult */
			return app('command.hub')->execute($generateClient, $sshExec);
		});

		$this->log(StageLogType::INFO, 'Parsing client credentials from command output...');
		[$clientId, $clientSecret] = $this->try(function () use ($passportClient) {
			$output = $passportClient->payload['stdout'] ?? '';
			$errorOutput = $passportClient->payload['stderr'] ?? '';

			if (empty($output)) {
				throw new RuntimeException(sprintf(
					'Passport client command failed! Error Output: "%s"',
					$errorOutput ?: 'No error message returned.'
				));
			}

			$this->log(StageLogType::INFO, sprintf('Raw passport command output: %s', $output));

			$search = preg_match('/Client ID\s*[.\s]+([a-f0-9\-]{36})\s*\n\s*Client secret\s*[.\s]+([\w\d]+)/i', $output, $matches);
			if (!$search || !isset($matches[1], $matches[2])) {
				throw new RuntimeException(sprintf(
					'Could not find Client ID and Client Secret in stdout! Full Output: "%s"',
					$output
				));
			}

			return [trim($matches[1]), trim($matches[2])];
		});

		$instanceId = $this->get('instance_id');

		$this->log(StageLogType::INFO, sprintf(
			'Successfully parsed values client_id=%s, client_secret=%s, instance_id=%s',
			$clientId,
			$clientSecret,
			$instanceId
		));

		// Update the database to include instance_id
		$this->log(StageLogType::INFO, 'Updating client with instance_id...');
		$this->try(function () use ($sshExec, $clientId, $instanceId) {
			$containerName = $this->get('container_name');
			$updateCommand = new SshCommand(sprintf(
				'docker exec %s php artisan tinker --execute="echo DB::table(\'oauth_clients\')->where(\'id\', \'%s\')->update([\'instance_id\' => \'%s\']);"',
				$containerName,
				$clientId,
				$instanceId
			));

			/** @var \App\System\Runtime\Commands\Data\CommandExecResult */
			$updateResult = app('command.hub')->execute($updateCommand, $sshExec);
			if (strpos($updateResult->payload['stdout'] ?? '', '1') === false) {
				throw new RuntimeException('Failed to update instance_id for the OAuth client.');
			}
		});

		$this->set('client_id', $clientId);
		$this->set('client_secret', $clientSecret);
	}

	public function rollback(): void
	{
		$this->log(StageLogType::WARNING, 'Rollback not supported!');
	}
}
