<?php

namespace App\System\Installs\Stages\Implementations\Docker\Support;

use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\Traits\RequiresSshExec;

class IssueUpCommand
{
    use RequiresSshExec;

    public function __construct()
    {
        //
    }

    public function execute(string $env, string $yaml, bool $recreate): CommandExecResult
    {
        $command = sprintf(
            'docker compose --env-file %s -f %s up -d',
            $env,
            $yaml,
        );

        if ($recreate) {
            $command .= ' --force-recreate';
        }

        $startDockerCompose = new SshCommand($command);

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        return app('command.hub')->execute($startDockerCompose, $this->sshExec);
    }
}
