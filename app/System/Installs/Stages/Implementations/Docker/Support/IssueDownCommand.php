<?php

namespace App\System\Installs\Stages\Implementations\Docker\Support;

use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\Traits\RequiresSshExec;
use Illuminate\Support\Arr;

class IssueDownCommand
{
    use RequiresSshExec;

    public function __construct()
    {
        //
    }

    public function execute(string $env, string $yaml): void
    {
        $stopDockerCompose = new SshCommand(sprintf(
            'docker compose --env-file %s -f %s down',
            $env,
            $yaml,
        ));

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($stopDockerCompose, $this->sshExec);

        // TODO Parse stdout.
        $stdout = Arr::get($result->payload, 'stdout');
    }
}
