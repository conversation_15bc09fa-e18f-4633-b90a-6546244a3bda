<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;

class DeleteDmsInstanceAndGroup implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Clearing saved data if found...');
        $this->try(function () {
            $groupId = $this->get('group_id', required: false);
            if ($groupId) {
                Group::where('id', '=', $groupId)->delete();
            }
            $instanceId = $this->get('instance_id', required: false);
            if ($instanceId) {
                Instance::where('id', '=', $instanceId)->delete();
            }
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
