<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\Models\Infrastructure\Instance;
use App\System\Infrastructure\Instances\Data\OauthClient;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Filesystem\Support\ReplaceEnvValues;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use RuntimeException;

class RearrangeDmsForPwa implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating DMS instance...');
        $instance = Instance::findOrFail($this->get('dms_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        $this->log(StageLogType::INFO, 'Generating PWA OAuth credentials...');
        $result = $this->try(function () {
            $generatePwaOauthCredentials = new SshCommand(sprintf(
                'docker exec -t --user www-data dms-dmsphpfpm-1 sh -c "%s"',
                'php artisan deploy:install:generate-pwa-client',
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($generatePwaOauthCredentials, $this->sshExec);
        });

        $this->log(StageLogType::INFO, 'Parsing PWA credentials from command output...');
        $this->try(function () use ($result) {
            if (empty($result->payload['stdout'])) {
                throw new RuntimeException('Failed to parse generate-pwa-client output - STDOUT is empty!');
            }

            $newCredentials = preg_match(
                '/PWA Client ID:[^\s]+ (?<id>[^\\\]+)\\r\\n.*PWA Client secret:[^\s]+ (?<secret>[^\\\]+)\\r\\n/',
                $result->payload['stdout'],
                $matches,
            );

            if ($newCredentials === false || empty($matches['id']) || empty($matches['secret'])) {
                throw new RuntimeException(
                    'Failed to parse generate-pwa-client output - missing id and/or secret keys!',
                );
            }

            $this->set('result_pwa_client_id', $matches['id']);
            $this->set('result_pwa_client_secret', $matches['secret']);
        });

        $this->log(StageLogType::INFO, 'Updating DMS instance data and storing changes to database...');
        $this->try(function () use ($instance) {
            $newData = $instance->data->mergeOauthClient('pwa', new OauthClient(
                $this->get('result_pwa_client_id'),
                $this->get('result_pwa_client_secret'),
            ));

            $instance->data = $newData;
            $instance->save();
        });

        $env = 'docker-compose.env';
        $yaml = 'docker-compose.yaml';

        $this->log(StageLogType::INFO, 'Enabling HAS_PWA setting in env file...');
        $this->try(function () use ($env) {
            app(ReplaceEnvValues::class)
                ->provideSshExec($this->sshExec)
                ->replace($env, [
                    'HAS_PWA' => '1',
                ], escape: false);
        });

        $this->log(StageLogType::INFO, 'Recreating DMS containers...');
        $this->try(function () use ($env, $yaml) {
            $startDockerCompose = new SshCommand(sprintf(
                'docker compose --env-file %s -f %s up -d --force-recreate',
                $env,
                $yaml,
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($startDockerCompose, $this->sshExec);
        });

        sleep(2);
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
