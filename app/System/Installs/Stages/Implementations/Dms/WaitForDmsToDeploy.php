<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\Support\Perform;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Exceptions\CommandFailedException;
use Illuminate\Support\Carbon;
use RuntimeException;

class WaitForDmsToDeploy implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $startedAt = Carbon::createFromTimestamp(time())->subMinutes(5);

        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client to monitor DMS containers...');
        $this->try(function () {
            $this->initializeSshExec(
                $this->get('host'),
                $this->get('user'),
                $this->get('private_key'),
            );
        });

        /**
         * Health checks.
         */

        $this->log(StageLogType::INFO, 'Checking docker compose status...');
        $this->try(function () {
            $dockerComposeLs = new SshCommand('docker compose ls');

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $ls = app('command.hub')->execute($dockerComposeLs, $this->sshExec);

            // Making sure all 4 DMS services are running.
            $search = preg_match('/dms\s+running\(4\)/si', $ls->payload['stdout'] ?? '');

            $this->log(StageLogType::INFO, 'Reading logs...');
            $logsCommand = new SshCommand(sprintf(
                'docker compose --env-file docker-compose.env -f docker-compose.yaml logs -n 100',
            ));
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            app('command.hub')->execute($logsCommand, $this->sshExec);

            if ($search === false) {
                $this->log(StageLogType::ERROR, 'Not all DMS docker compose services has been found running!');

                throw new RuntimeException('Aborting deploy...');
            }
        });

        /**
         * Waiting for installation to end, parsing data.
         */

        $this->log(StageLogType::INFO, 'Waiting for DMS entrypoint script to finish...');
        $lastCheck = $startedAt->copy();
        Perform::task(function () use (&$lastCheck) {
            return $this->try(function () use (&$lastCheck) {
                $newLastCheck = Carbon::createFromTimestamp(time())->subSecond();
                $searchingForSuccess = new SshCommand(sprintf(
                    'docker logs --since "%s" dms-dmsphpfpm-1 | grep "docker-entrypoint_SUCCESS"',
                    $lastCheck->toIso8601ZuluString(),
                ));

                $result = app('command.hub')->execute($searchingForSuccess, $this->sshExec);
                $lastCheck = $newLastCheck;

                return ! empty($result->payload['stdout']);
            });
        })
            ->onSuccess(fn () => $this->log(StageLogType::INFO, 'Found message about entrypoint script success!'))
            ->onFailure(function ($e) {
                $this->log(
                    StageLogType::ERROR,
                    'Exceeded allowed number of tries to wait for entrypoint script success! Aborting...',
                );

                throw $e;
            })
            ->retryEvery(seconds: 30)
            ->forAtLeast(times: 20);

        $this->log(StageLogType::INFO, 'Running installation command to retrieve data...');
        $result = $this->try(function () {
            $installationCommand = new SshCommand(sprintf(
                'docker exec -t --user www-data dms-dmsphpfpm-1 sh -c "%s"',
                sprintf(
                    'php artisan deploy:install:run \"%s\" \"%s\" \"%s\"',
                    $this->get('email'),
                    $this->get('company_name'),
                    $this->get('with_pwa', false, false) ? '1' : '0',
                ),
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $result = app('command.hub')->execute($installationCommand, $this->sshExec);

            // If we found needed result info from DMS then we can proceed to parsing it.
            if (! empty($result->payload['stdout'])) {
                $this->log(StageLogType::INFO, 'Found message from DMS about successfull installation!');

                return $result->payload['stdout'];
            } else {
                throw new RuntimeException('No data retrieved from installation command! STDOUD is empty!');
            }
        });

        $this->log(StageLogType::INFO, 'Parsing and saving DMS data...', ['input' => $result]);
        $this->try(function () use ($result) {
            $foundJsonData = preg_match('/result = (?<json>{.*})/', $result, $matches);
            if ($foundJsonData === false) {
                throw new RuntimeException('Provided DMS data is not a valid JSON! Aborting...');
            }

            $dmsData = json_decode($matches['json'], true);
            if (empty($dmsData)) {
                throw new RuntimeException('Provided DMS data is empty! Something went wrong. Aborting...');
            }

            $this->log(StageLogType::INFO, 'Successfully parsed DMS data. Saving...', $dmsData);

            $this->set('result_email', $dmsData['EMAIL']);
            $this->set('result_hub_client_id', $dmsData['HUB_CLIENT_ID']);
            $this->set('result_hub_client_secret', $dmsData['HUB_CLIENT_SECRET']);

            // Additionally write down PWA information if requested.
            if ($this->get('with_pwa', false, false)) {
                $this->set('result_pwa_client_id', $dmsData['PWA_CLIENT_ID']);
                $this->set('result_pwa_client_secret', $dmsData['PWA_CLIENT_SECRET']);
            }
        });

        // Wait for webserver only if there is no PWA. Otherwise no need to wait because we should proceed with
        // PWA deployment right away.
        if (! $this->get('with_pwa', false, false)) {
            $this->log(StageLogType::INFO, 'Waiting for web server to finish initialization...');
            $this->try(function () {
                $idleTime = 0;
                while (true) {
                    // Waiting no more than 10 minutes for nginx to finish initialization.
                    if ($idleTime > 600) {
                        throw new RuntimeException(
                            'Max wait time for web server initialization has passed. Something went wrong. Aborting',
                        );
                    }

                    $result = $this->runSilenced(function () use (&$idleTime) {
                        try {
                            $searchingForStartCommand = new SshCommand(sprintf(
                                'docker compose --file docker-compose.yaml --env-file docker-compose.env logs nginx | grep "start worker processes"',
                            ));
                            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
                            return app('command.hub')->execute($searchingForStartCommand, $this->sshExec);
                        } catch (CommandFailedException $e) {
                            // Exit code 1 for grep means no match was found, meaning we need to continue waiting and polling.
                            if ($e->error->code === 1) {
                                $idleTime += 10;
                                sleep(10);

                                return;
                            }

                            throw $e;
                        }
                    });

                    // If we found needed start info from nginx then we can end this stage.
                    if (! empty($result->payload['stdout'])) {
                        $this->log(StageLogType::INFO, 'Found message from web server about successfull initialization!');

                        return $result->payload['stdout'];
                    } else {
                        $this->runSilenced(function () {
                            $showingLatestProgressCommand = new SshCommand(sprintf(
                                'docker compose --file docker-compose.yaml --env-file docker-compose.env logs nginx -n 50',
                            ));
                            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
                            app('command.hub')->execute($showingLatestProgressCommand, $this->sshExec);
                        });
                    }

                    $idleTime += 10;
                    sleep(10);
                }
            });
        }
    }

    public function rollback(): void
    {
        // TODO maybe stop all containers to prevent constant restart?

        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
