<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Data\DmsInstanceData;
use App\System\Infrastructure\Instances\Data\Hosting;
use App\System\Infrastructure\Instances\Data\OauthClient;
use App\System\Infrastructure\Instances\Data\PusherData;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * READS:
 *  name - name of this DMS instance.
 *  subdomain - subdomain of this DMS instance.
 *  hostname - hostname of this DMS instance.
 *  url - full URL of this DMS instance.
 *  group_id - group ID to create new server in (optional).
 *  new_group_name - name for a new group to create new server in (required if group_id empty).
 *  server_public_ip - public IP address of the server with DMS instance.
 *  server_external_id - external ID of the server with DMS instance.
 *  server_external_name - external name of the server with DMS instance.
 *  oauth - OAuth data for this DMS instance's API (PWA keys, HUB keys etc.).
 *  pusher - Pusher app data of this DMS instance.
 *
 * WRITES:
 *  instance_id - instance ID of the new server.
 *  group_id - group ID new server's instance belongs to.
 */
class CreateDmsInstance implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Saving DMS instance to database...');
        $this->try(function () {
            DB::transaction(function () {
                $groupId = $this->get('group_id', required: false);
                if (! $groupId) {
                    $groupName = $this->get('new_group_name');

                    if (Group::where('name', '=', $groupName)->exists()) {
                        throw new RuntimeException(sprintf('Group with name %s already exists!', $groupName));
                    }

                    $group = Group::create([
                        'name' => $groupName,
                    ]);

                    $this->set('group_id', $group->id);
                } else {
                    $group = Group::findOrFail($groupId);
                }

                // Gathering available OAuth data.
                $oauthClients = [
                    'hub' => new OauthClient(
                        $this->get('oauth_hub_client_id'),
                        $this->get('oauth_hub_client_secret'),
                    ),
                ];
                $pwaClientId = $this->get('oauth_pwa_client_id', required: false);
                if (! empty($pwaClientId)) {
                    $oauthClients['pwa'] = new OauthClient(
                        $pwaClientId,
                        $this->get('oauth_pwa_client_secret'),
                    );
                }

                // Gathering Pusher data.
                $pusherData = new PusherData(
                    $this->get('pusher_app_key'),
                    $this->get('pusher_app_id'),
                    $this->get('pusher_app_secret'),
                );

                // Creating new instance.
                $instance = new Instance();
                $instance->group()->associate($group);
                $instance->app_type = AppType::DMS;
                $instance->name = 'dms';
                $instance->data = new DmsInstanceData(
                    $this->get('name'),
                    $this->get('subdomain'),
                    $this->get('hostname'),
                    $this->get('url'),
                    Hosting::HETZNER_CLOUD,
                    $this->get('server_public_ip'),
                    $this->get('server_external_id'),
                    $this->get('server_external_name'),
                    $oauthClients,
                    $pusherData,
                );

                $instance->save();

                // Creating SSH connection if SSH data is provided for server.
                $sshUser = $this->get('ssh_user', required: false);
                $privateKey = $this->get('private_key', required: false);
                if ($sshUser && $privateKey) {
                    $data = [
                        'host' => $this->get('server_public_ip'),
                        'user' => $sshUser,
                        'private_key' => $privateKey,
                        'timeout' => null,
                        'remove_bash' => false,
                    ];

                    $this->log(
                        StageLogType::INFO,
                        'Found SSH connection data, saving to database for this server...',
                        $data,
                    );

                    $sshConnection = SshConnection::create($data);
                    $instance->sshConnections()->attach($sshConnection);
                }

                $this->set('instance_id', $instance->id);
            });
        });
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Clearing saved data if found...');
        $this->try(function () {
            $groupId = $this->get('group_id', required: false);
            if ($groupId) {
                Group::where('id', '=', $groupId)->delete();
            }
            $instanceId = $this->get('instance_id', required: false);
            if ($instanceId) {
                Instance::where('id', '=', $instanceId)->delete();
            }
        }, throw: false);
    }
}
