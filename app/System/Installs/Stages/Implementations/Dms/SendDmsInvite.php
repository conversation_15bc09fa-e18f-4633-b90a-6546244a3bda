<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;

class SendDmsInvite implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client to send DMS invites...');
        $this->try(function () {
            $this->initializeSshExec(
                $this->get('host'),
                $this->get('user'),
                $this->get('private_key'),
            );
        });

        $this->log(StageLogType::INFO, 'Sending DMS invites...');
        $this->try(function () {
            $sendInvite = new SshCommand('docker exec dms-dmsphpfpm-1 php artisan deploy:install:notify-root-user');

            app('command.hub')->execute($sendInvite, $this->sshExec);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
