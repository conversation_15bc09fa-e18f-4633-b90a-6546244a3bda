<?php

namespace App\System\Installs\Stages\Implementations\Dms;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Minio\Support\Buckets;
use App\System\Installs\Stages\Implementations\Sentry\Support\Projects;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;

class DeleteDmsResources implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Deleting Minio bucket...');
        $this->try(function () {
            $bucketName = $this->get('bucket.bucket_name', required: false);
            if (empty($bucketName)) {
                $this->log(StageLogType::INFO, 'No created Minio bucket found. Nothing to do.');

                return;
            }

            $this->log(StageLogType::INFO, sprintf('Found Minio bucket by name "%s". Deleting...', $bucketName));

            (new Buckets())->removeBucket(
                $this->get('bucket.username'),
                $bucketName,
                $this->get('bucket.policyname'),
            );

            $this->log(StageLogType::INFO, 'Minio bucket successfully deleted!');
        }, throw: false);

        $this->log(StageLogType::INFO, 'Rolling back Sentry changes for this instance...');
        $this->try(function () {
            $projectSlug = $this->get('sentry.project_slug', required: false);
            if (empty($projectSlug)) {
                $this->log(StageLogType::INFO, 'No created Sentry project found. Nothing to do.');

                return;
            }

            $this->log(StageLogType::INFO, sprintf('Found Sentry project by slug "%s". Deleting...', $projectSlug));

            (new Projects())->deleteProject(
                $this->get('sentry.organization_slug'),
                $projectSlug,
            );

            $this->log(StageLogType::INFO, 'Sentry project successfully deleted!');
        }, throw: false);
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
