<?php

namespace App\System\Installs\Stages\Implementations\Minio\Support;

use App\System\Installs\Stages\Implementations\Minio\Data\Bucket;
use App\System\Installs\Stages\Implementations\Minio\Data\CdnApiAction;
use App\System\Installs\Stages\Implementations\Minio\Factories\CdnApiCommandFactory;

class Buckets
{
    public function createBucket(
        string $username,
        string $userpassword,
        string $bucketname,
        string $policyname,
    ): Bucket {
        $createBucket = (new CdnApiCommandFactory())->create(
            CdnApiAction::CREATE_BUCKET,
            data: [
                'NEW_USER_NAME' => $username,
                'NEW_USER_PASSWORD' => $userpassword,
                'NEW_BUCKET_NAME' => $bucketname,
                'NEW_POLICY_NAME' => $policyname,
                'CREATE_DMS' => true,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createBucket);

        return Bucket::createFromData($result->payload['keys_info']);
    }

    public function removeBucket(string $username, string $bucketname, string $policyname): void
    {
        $removeBucket = (new CdnApiCommandFactory())->create(
            CdnApiAction::REMOVE_BUCKET,
            data: [
                'USER_NAME' => $username,
                'BUCKET_NAME' => $bucketname,
                'POLICY_NAME' => $policyname,
            ],
        );

        app('command.hub')->execute($removeBucket);
    }
}
