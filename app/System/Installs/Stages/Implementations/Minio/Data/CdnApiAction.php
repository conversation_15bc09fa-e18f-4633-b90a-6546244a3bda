<?php

namespace App\System\Installs\Stages\Implementations\Minio\Data;

use App\System\Runtime\Commands\Data\ApiMethod;
use LogicException;

enum CdnApiAction: string
{
    case CREATE_BUCKET = 'create_bucket';
    case REMOVE_BUCKET = 'remove_bucket';

    public function requiresAuth(): bool
    {
        return true;
    }

    public function route(array $params = []): string
    {
        return match ($this) {
            // Projects.
            self::CREATE_BUCKET => '/create_bucket',
            self::REMOVE_BUCKET => '/remove_bucket',

            default => throw new LogicException(sprintf('Missing route for CDN API action "%s"!', $this->value)),
        };
    }

    public function apiMethod(): ApiMethod
    {
        return match ($this) {
            // Servers.
            self::CREATE_BUCKET => ApiMethod::POST,
            self::REMOVE_BUCKET => ApiMethod::POST,

            default => throw new LogicException(sprintf('Missing API method for CDN API action "%s"!', $this->value)),
        };
    }
}
