<?php

namespace App\System\Installs\Stages\Implementations\Minio\Data;

class Bucket
{
    public function __construct(
        public readonly string $name,
        public readonly string $user,
        public readonly string $accessKey,
        public readonly string $secretKey,
        public readonly array $data,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['Bucket'],
            $data['User'],
            $data['Access Key'],
            $data['Secret Key'],
            $data,
        );
    }
}
