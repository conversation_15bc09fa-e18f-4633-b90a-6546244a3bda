<?php

namespace App\System\Installs\Stages\Implementations\Minio\Factories;

use App\System\Installs\Stages\Implementations\Minio\Data\CdnApiAction;
use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\BasicAuth;

class CdnApiCommandFactory
{
    public function create(CdnApiAction $action, array $data = [], array $routeParams = []): ApiCommand
    {
        $headers = [
            'Content-Type' => 'application/json',
        ];

        if ($action->requiresAuth()) {
            $auth = new BasicAuth(config('deploy.cdn.basic_auth.user'), config('deploy.cdn.basic_auth.password'));
        } else {
            $auth = null;
        }

        $data['MINIO_HOST'] = config('deploy.cdn.minio_host');
        $data['CICD_ACCESS_KEY'] = config('deploy.cdn.cicd_access_key');
        $data['CICD_SECRET_KEY'] = config('deploy.cdn.cicd_secret_key');

        return app(ApiCommand::class, [
            'url' => config('deploy.cdn.url') . $action->route($routeParams),
            'method' => $action->apiMethod(),
            'data' => $data,
            'headers' => $headers,
            'auth' => $auth,
        ]);
    }
}
