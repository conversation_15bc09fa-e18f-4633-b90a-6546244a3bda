<?php

namespace App\System\Installs\Stages\Implementations\Minio;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Minio\Support\Buckets;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;

class CreateBucket implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Creating new Minio bucket...');
        /** @var \App\System\Installs\Stages\Implementations\Minio\Data\Bucket */
        $bucket = $this->try(function () {
            return (new Buckets())->createBucket(
                $this->get('username'),
                $this->get('userpassword'),
                $this->get('bucketname'),
                $this->get('policyname'),
            );
        });

        $this->log(StageLogType::INFO, 'Saving created bucket info...');
        $this->try(function () use ($bucket) {
            $this->set('bucket_name', $bucket->name);
            $this->set('bucket_user', $bucket->user);
            $this->set('bucket_access_key', $bucket->accessKey);
            $this->set('bucket_secret_key', $bucket->secretKey);
        });
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Rolling back Minio changes for this instance...');
        $this->try(function () {
            $bucketName = $this->get('bucket_name', required: false);
            if (empty($bucketName)) {
                $this->log(StageLogType::INFO, 'No created Minio bucket found. Nothing to do.');

                return;
            }

            $this->log(StageLogType::INFO, sprintf('Found Minio bucket by name "%s". Deleting...', $bucketName));

            (new Buckets())->removeBucket(
                $this->get('username'),
                $bucketName,
                $this->get('policyname'),
            );

            $this->log(StageLogType::INFO, 'Minio bucket successfully deleted!');
        }, throw: false);
    }
}
