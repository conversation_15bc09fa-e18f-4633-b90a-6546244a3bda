<?php

namespace App\System\Installs\Stages\Implementations\Pwa;

use App\Models\Infrastructure\Instance;
use App\Support\Perform;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;
use Illuminate\Support\Carbon;

class StopAndUpgradePwa implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $startedAt = Carbon::createFromTimestamp(time());

        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating provided instance...');
        $instance = Instance::findOrFail($this->get('instance_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        /**
         * Health checks.
         */

        $this->log(StageLogType::INFO, 'Waiting until PWA php-fpm container is available...');
        Perform::task(function () {
            return $this->try(function () {
                $dockerPs = new SshCommand('docker ps --filter "name=pwa-pwaphpfpm-1" --filter="status=running" -q');

                /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
                $ps = app('command.hub')->execute($dockerPs, $this->sshExec);

                return ! empty($ps->payload['stdout']);
            });
        })
            ->onSuccess(fn () => $this->log(StageLogType::INFO, 'PWA php-fpm container found to be running! Success!'))
            ->onFailure(function ($e) {
                $this->log(StageLogType::ERROR, 'Exceeded allowed number of tries to wait for PWA php-fpm to be available! Aborting...');

                throw $e;
            })
            ->retryEvery(seconds: 10)
            ->forAtLeast(times: 15);

        $this->log(StageLogType::INFO, 'Waiting for PWA entrypoint script to finish...');
        $lastCheck = $startedAt->copy();
        Perform::task(function () use (&$lastCheck) {
            return $this->try(function () use (&$lastCheck) {
                $newLastCheck = Carbon::createFromTimestamp(time())->subSecond();
                $searchingForSuccess = new SshCommand(sprintf(
                    'docker logs --since "%s" pwa-pwaphpfpm-1 | grep "docker-entrypoint_SUCCESS"',
                    $lastCheck->toIso8601ZuluString(),
                ));

                $result = app('command.hub')->execute($searchingForSuccess, $this->sshExec);
                $lastCheck = $newLastCheck;

                return ! empty($result->payload['stdout']);
            });
        })
            ->onSuccess(fn () => $this->log(StageLogType::INFO, 'Found message about entrypoint script success!'))
            ->onFailure(function ($e) {
                $this->log(
                    StageLogType::ERROR,
                    'Exceeded allowed number of tries to wait for entrypoint script success! Aborting...',
                );

                throw $e;
            })
            ->retryEvery(seconds: 20)
            ->forAtLeast(times: 15);

        $this->log(StageLogType::INFO, 'Stopping PWA app...');
        $this->try(function () {
            $artisanDown = new SshCommand(sprintf(
                'docker exec -t --user www-data pwa-pwaphpfpm-1 sh -c "%s"',
                'php artisan down',
            ));

            app('command.hub')->execute($artisanDown, $this->sshExec);
        });

        $this->log(StageLogType::INFO, 'Executing upgrade command...');
        $this->try(function () {
            $upgrade = new SshCommand('docker exec -t --user www-data pwa-pwaphpfpm-1 sh -c "php artisan update"');

            try {
                app('command.hub')->execute($upgrade, $this->sshExec);
            } catch (\Exception $e) {
                // We are reporting internal errors, but can not rollback the upgrade - internal errors for PWA
                // might mean that some stages were executed partially and thus it needs custom attention from
                // developers to assess the situation and fix it.
                report($e);
            }
        });

        $this->log(StageLogType::INFO, 'Activating PWA app back again...');
        $this->try(function () {
            $artisanUp = new SshCommand(sprintf(
                'docker exec -t --user www-data pwa-pwaphpfpm-1 sh -c "%s"',
                'php artisan up',
            ));

            app('command.hub')->execute($artisanUp, $this->sshExec);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
