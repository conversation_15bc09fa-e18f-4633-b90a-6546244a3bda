<?php

namespace App\System\Installs\Stages\Implementations\Pwa;

use App\Models\Infrastructure\Instance;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;

/**
 * READS:
 *  dms_id - DMS instance ID to active PWA for.
 *  base_url - PWA instance base url.
 *  pwa_client_id - PWA instance client ID for oauth API.
 *  pwa_client_secret - PWA instance client secret for oauth API.
 *
 * WRITES:
 *  -
 */
class ActivatePwaInDms implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Validating DMS instance...');
        $instance = Instance::findOrFail($this->get('dms_id'));

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () use ($instance) {
            $this->initializeSshExecFromInstance($instance);
        });

        $this->log(StageLogType::INFO, 'Running PWA activation command...');
        $this->try(function () {
            $activatePwaCommand = new SshCommand(sprintf(
                'docker exec -t --user www-data dms-dmsphpfpm-1 sh -c "%s"',
                sprintf(
                    'php artisan deploy:install:activate-pwa %s %s %s',
                    $this->get('pwa_client_id'),
                    $this->get('pwa_client_secret'),
                    $this->get('base_url'),
                ),
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($activatePwaCommand, $this->sshExec);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
