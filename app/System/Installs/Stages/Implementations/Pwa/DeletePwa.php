<?php

namespace App\System\Installs\Stages\Implementations\Pwa;

use App\Models\Infrastructure\Instance;
use App\Support\Perform;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Pwa\Traits\InteractsWithPwaApi;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\ApiMethod;

class DeletePwa implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use InteractsWithPwaApi;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Deleting PWA record in database...');
        $this->try(function () {
            $instance = Instance::findOrFail($this->get('pwa_id'));
            $instance->delete();
        }, throw: false);

        $this->log(StageLogType::INFO, 'Deleting PWA record in admin part...');
        $this->try(function () {
            $pwaClientId = $this->get('data.client_id', required: false);
            if (empty($pwaClientId)) {
                $this->log(StageLogType::INFO, 'No existing PWA record found, skipping deletion...');

                return;
            }

            Perform::task(function () use ($pwaClientId) {
                $token = $this->getAccessToken();

                $removePwa = app(ApiCommand::class, [
                    'url' => sprintf('%s/api/v1/pwa/%s', config('deploy.pwa.url'), $pwaClientId),
                    'method' => ApiMethod::DELETE,
                    'data' => [],
                    'headers' => [
                        'Accept' => 'application/json',
                        'Authorization' => sprintf('Bearer %s', $token),
                    ],
                ]);

                $this->sendRequest($removePwa);
            })
                ->onSuccess(fn () => $this->log(StageLogType::INFO, 'Deleting PWA record in admin part...'))
                ->onFailure(fn () => $this->log(StageLogType::WARNING, 'Exceeded allowed number of tries to delete PWA record!'))
                ->retryEvery(seconds: 1)
                ->forAtLeast(times: 3);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
