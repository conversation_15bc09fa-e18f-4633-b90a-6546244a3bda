<?php

namespace App\System\Installs\Stages\Implementations\Pwa;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Installs\Stages\Traits\UtilizesSsh;
use App\System\Runtime\Commands\SshCommand;

class FinishPwaInstallation implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use UtilizesSsh;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Initializing SSH client...');
        $this->try(function () {
            $this->initializeSshExec(
                $this->get('host'),
                $this->get('user'),
                $this->get('private_key'),
            );
        });

        $this->log(StageLogType::INFO, 'Installing PWA dictionaries...');
        $this->try(function () {
            $importAllCommand = new SshCommand(sprintf(
                'docker exec -t --user www-data pwa-pwaphpfpm-1 sh -c "%s"',
                'php artisan import:all',
            ));

            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            return app('command.hub')->execute($importAllCommand, $this->sshExec);
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
