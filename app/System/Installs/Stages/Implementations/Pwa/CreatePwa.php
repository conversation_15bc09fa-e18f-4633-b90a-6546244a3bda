<?php

namespace App\System\Installs\Stages\Implementations\Pwa;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\Support\Perform;
use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Data\OauthClient;
use App\System\Infrastructure\Instances\Data\PwaInstanceData;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Pwa\Traits\InteractsWithPwaApi;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\ApiMethod;
use RuntimeException;

/**
 * READS:
 *  name - name of the new PWA instance.
 *  api_client_id - client ID to admin PWA API.
 *  api_client_secret - its client secret.
 *  dms_url - url to DMS instance that we are deploying new PWA for.
 *  dms_client_id - client ID to this DMS instance.
 *  dms_client_secret - its client secret.
 *  dms_pusher_app_key - Pusher key of this DMS instance.
 *  dms_pusher_app_secret - its secret.
 *  group_id - group ID to create new PWA instance in.
 *
 * WRITES:
 *  url - new PWA url.
 *  oauth_callback - oauth callback to use with new PWA.
 *  client_id - client ID registered within new PWA.
 *  secret_key - secret key for client ID within new PWA.
 *  instance_id - newly created PWA instance ID.
 */
class CreatePwa implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;
    use InteractsWithPwaApi;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Fetching required attributes for PWA installation...');
        $data = $this->try(function () {
            return [
                'name' => $this->get('name'),
                'subdomain' => $this->get('subdomain'),
                'dms_host' => $this->get('dms_url'),
                'dms_client_id' => $this->get('dms_client_id'),
                'dms_client_secret' => $this->get('dms_client_secret'),
                'dms_pusher_app_key' => $this->get('dms_pusher_app_key'),
                'dms_pusher_app_secret' => $this->get('dms_pusher_app_secret'),
            ];
        });

        $this->log(StageLogType::INFO, 'Checking infrastructure records...');
        $group = $this->try(function () {
            $groupId = $this->get('group_id');

            return Group::findOrFail($groupId);
        });

        $this->log(StageLogType::INFO, 'Sending request to PWA provider...');
        $pwaData = $this->try(function () use ($data) {
            $tries = 0;
            while ($tries < 3) {
                $tries++;

                $token = $this->getAccessToken();

                $addPwa = app(ApiCommand::class, [
                    'url' => sprintf('%s/api/v1/pwa/add', config('deploy.pwa.url')),
                    'method' => ApiMethod::POST,
                    'data' => $data,
                    'headers' => [
                        'Accept' => 'application/json',
                        'Authorization' => sprintf('Bearer %s', $token),
                    ],
                ]);

                return $this->sendRequest($addPwa);
            }

            throw new RuntimeException('Exceed number of allowed PWA requests! Aborting...');
        });

        $this->log(StageLogType::INFO, 'Parsing and saving PWA data...', ['input' => $pwaData]);
        $this->try(function () use ($pwaData) {
            $this->set('url', $pwaData['pwa_link']);
            $this->set('oauth_callback', $pwaData['oauth_callback']);
            $this->set('client_id', $pwaData['client_id']);
            $this->set('secret_key', $pwaData['secret_key']);
        });

        $this->log(StageLogType::INFO, 'Saving to database...');
        $this->try(function () use ($group) {
            $instance = new Instance();
            $instance->group()->associate($group);
            $instance->app_type = AppType::PWA;
            $instance->name = 'pwa';
            $instance->data = new PwaInstanceData(
                $this->get('name'),
                $this->get('subdomain'),
                $this->get('url'),
                [
                    'dms' => new OauthClient(
                        $this->get('client_id'),
                        $this->get('secret_key'),
                    ),
                ],
            );
            $instance->save();

            $sshConnection = SshConnection::findOrFail($this->get('ssh_connection_id'));
            $instance->sshConnections()->attach($sshConnection);

            $this->set('instance_id', $instance->id);
        });
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Deleting PWA record in admin part...');
        $this->try(function () {
            $pwaClientId = $this->get('client_id', required: false);
            if (empty($pwaClientId)) {
                $this->log(StageLogType::INFO, 'No existing PWA record found, skipping deletion...');

                return;
            }

            Perform::task(function () use ($pwaClientId) {
                $token = $this->getAccessToken();

                $removePwa = app(ApiCommand::class, [
                    'url' => sprintf('%s/api/v1/pwa/%s', config('deploy.pwa.url'), $pwaClientId),
                    'method' => ApiMethod::DELETE,
                    'data' => [],
                    'headers' => [
                        'Accept' => 'application/json',
                        'Authorization' => sprintf('Bearer %s', $token),
                    ],
                ]);

                $this->sendRequest($removePwa);
            })
                ->onSuccess(fn () => $this->log(StageLogType::INFO, 'Deleting PWA record in admin part...'))
                ->onFailure(fn () => $this->log(StageLogType::WARNING, 'Exceeded allowed number of tries to delete PWA record!'))
                ->retryEvery(seconds: 1)
                ->forAtLeast(times: 3);
        }, throw: false);
    }
}
