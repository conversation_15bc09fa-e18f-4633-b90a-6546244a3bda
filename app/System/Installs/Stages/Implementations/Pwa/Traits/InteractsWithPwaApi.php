<?php

namespace App\System\Installs\Stages\Implementations\Pwa\Traits;

use Illuminate\Support\Facades\Cache;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Runtime\Exceptions\CommandFailedException;
use Symfony\Component\HttpFoundation\Response;
use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\ApiMethod;
use RuntimeException;

trait InteractsWithPwaApi
{
    private function obtainNewAccessToken(): string
    {
        $obtainToken = app(ApiCommand::class, [
            'url' => sprintf('%s/api/v1/token', config('deploy.pwa.url')),
            'method' => ApiMethod::POST,
            'data' => [
                'client_id' => $this->get('api_client_id'),
                'client_secret' => $this->get('api_client_secret'),
            ],
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
        ]);

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($obtainToken);
        $token = $result->payload['access_token'] ?? null;
        $type = $result->payload['token_type'] ?? null;

        if (! $token) {
            throw new RuntimeException('Failed to obtain PWA access token! Aborting...');
        }
        if ($type !== 'Bearer') {
            throw new RuntimeException('Unsupported PWA access token type! Aborting...');
        }

        return $token;
    }

    private function getAccessToken(): string
    {
        $token = Cache::rememberForever('pwa_api_token', function () {
            $this->log(StageLogType::INFO, 'Obtaining new PWA access token...');

            return $this->obtainNewAccessToken();
        });

        return $token;
    }

    private function sendRequest($request, bool $retry = false): array
    {
        try {
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $result = app('command.hub')->execute($request);

            return $result->payload;
        } catch (CommandFailedException $e) {
            if ($e->error->code === Response::HTTP_UNAUTHORIZED && ! $retry) {
                $this->log(StageLogType::WARNING, 'PWA access token is outdated!');
                Cache::forget('pwa_api_token');

                $this->sendRequest($request, true);
            } else {
                throw $e;
            }
        }
    }
}
