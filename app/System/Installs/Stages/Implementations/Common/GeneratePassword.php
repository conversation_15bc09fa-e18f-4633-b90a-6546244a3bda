<?php

namespace App\System\Installs\Stages\Implementations\Common;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Str;
use InvalidArgumentException;

/**
 * READS:
 *  title* - Title to use in logging messages.
 *  key_names* - array of IDs of context key(s) that the resulting password(s) are going to be written to.
 *
 * WRITES:
 *  {each of key_names} - resulting passwords.
 */
class GeneratePassword implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $keys = $this->getKeys();

        $title = $this->get('title');
        $this->log(StageLogType::INFO, sprintf('Generating password(s) for %s...', $title));

        foreach ($keys as $key => $parameters) {
            $password = $this->try(function () use ($key, $parameters) {
                switch ($parameters['type']) {
                    case 'laravel_app_key':
                        return $this->generateLaravelAppKey($parameters);
                    case 'random_bin2hex':
                        return $this->generateRandomBin2Hex($parameters);
                    case 'password':
                        return $this->generatePassword($parameters);
                }

                throw new InvalidArgumentException(sprintf('Unknown type %s for %s password!', $parameters['type'], $key));
            });

            $this->log(StageLogType::INFO, sprintf('%s=%s', $key, $password));
            $this->set($key, $password);
        }
    }

    public function rollback(): void
    {
        $this->log(StageLogType::INFO, 'Nothing to rollback.');
    }

    private function generateRandomBin2Hex(array $parameters): string
    {
        $length = $parameters['length'] ?? 32;

        if ($length % 2 !== 0) {
            throw new InvalidArgumentException(sprintf(
                'Incorrect length value for random bin2hex generator! It must be divisible by 2!',
            ));
        }

        return bin2hex(random_bytes($length / 2));
    }

    private function generateLaravelAppKey(array $parameters): string
    {
        $cipher = $parameters['cipher'] ?? config('app.cipher');

        return 'base64:'.base64_encode(Encrypter::generateKey($cipher));
    }

    private function generatePassword(array $parameters): string
    {
        if (empty($parameters['include'])) {
            $letters = true;
            $numbers = true;
            $symbols = false;
            $spaces = false;
        } else {
            $letters = in_array('letters', $parameters['include']);
            $numbers = in_array('numbers', $parameters['include']);
            $symbols = in_array('symbols', $parameters['include']);
            $spaces = in_array('spaces', $parameters['include']);
        }

        $length = $parameters['length'] ?? 16;

        return Str::password($length, $letters, $numbers, $symbols, $spaces);
    }

    private function getKeys(): array
    {
        $keys = $this->get('key_names', required: true, default: []);

        foreach (array_keys($keys) as $key) {
            if (in_array($key, ['title', 'key_names'])) {
                throw new InvalidArgumentException(sprintf(
                    'Provided key_name of "%s" is a reserved word for other input parameters!',
                    $key,
                ));
            }
        }

        return $keys;
    }
}
