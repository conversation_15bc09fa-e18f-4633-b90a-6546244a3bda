<?php

namespace App\System\Installs\Stages\Implementations\Common;

use App\Support\Perform;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use Exception;

class CheckUrlStatus implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        [$url, $times, $every, $checkSsl] = $this->try(function () {
            return [
                $this->get('url'),
                $this->get('number_of_tries'),
                $this->get('try_every'),
                $this->get('check_ssl', false, false),
            ];
        });

        $this->log(StageLogType::INFO, sprintf(
            'Querying URL %s (max of %d times every %d seconds)...',
            $url,
            $times,
            $every,
        ));
        Perform::task(function () use ($url, $checkSsl) {
            $this->try(function () use ($url, $checkSsl) {
                $urlParse = parse_url($url);
                $domain = $urlParse['host'];

                $this->log(StageLogType::INFO, sprintf('Checking if DNS %s exists...', $domain));
                $dnsResult = checkdnsrr($domain, 'ANY');

                if ($dnsResult) {
                    $this->log(StageLogType::INFO, 'Success!');
                } else {
                    throw new Exception('DNS check failed!');
                }

                $this->log(
                    StageLogType::INFO,
                    sprintf('Checking if URL %s is accessible by retrieving headers...', $url),
                );
                $headers = get_headers($url);
                $headersResult = ! empty($headers);

                if ($headersResult) {
                    $this->log(StageLogType::INFO, 'Success!', $headers);
                } else {
                    throw new Exception('URL check failed!');
                }

                if ($checkSsl) {
                    $this->log(StageLogType::INFO, 'Checking its SSL certificate...');
                    $stream = stream_context_create([
                        'ssl' => ['capture_peer_cert' => true],
                        'http' => ['ignore_errors' => true],
                    ]);

                    $read = fopen($url, 'rb', false, $stream);
                    $context = stream_context_get_params($read);

                    $certificate = $context['options']['ssl']['peer_certificate'] ?? null;
                    $sslResult = ! empty($certificate);

                    if ($sslResult) {
                        $this->log(StageLogType::INFO, 'Success!', openssl_x509_parse($certificate));
                    } else {
                        throw new Exception('SSL check failed!');
                    }
                }
            });
        })
            ->onSuccess(fn () => $this->log(StageLogType::INFO, 'Success!'))
            ->onFailure(function ($e) {
                $this->log(StageLogType::ERROR, 'Exceeded allowed number of tries to check given URL! Aborting...');

                throw $e;
            })
            ->retryEvery(seconds: $every)
            ->forAtLeast(times: $times);
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
