<?php

namespace App\System\Installs\Stages\Implementations\Data;

use App\System\Installs\Stages\Contracts\StageImplementation;

enum StageImplementationMethod: string
{
    case RUN = 'run';
    case ROLLBACK = 'rollback';

    public function apply(StageImplementation $implementation): void
    {
        if ($this === self::RUN) {
            $implementation->run();
        } else {
            $implementation->rollback();
        }
    }
}
