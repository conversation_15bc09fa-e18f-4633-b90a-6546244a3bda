<?php

namespace App\System\Installs\Stages\Implementations\Versions;

use App\Models\Infrastructure\Instance;
use App\Models\Infrastructure\InstanceVersion;
use App\Models\Installs\Install;
use App\Models\Versions\Version;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use RuntimeException;

class AssignVersionToInstance implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Saving DMS instance to database...');
        $this->try(function () {
            $instance = Instance::findOrFail($this->get('instance_id'));
            $version = Version::findOrFail($this->get('version_id'));

            if ($instance->app_type !== $version->app_type) {
                throw new RuntimeException(sprintf(
                    'Version ID %d is incompatible with instance ID %d!',
                    $version->id,
                    $instance->id,
                ));
            }

            $instanceVersion = new InstanceVersion();
            $instanceVersion->instance()->associate($instance);
            $instanceVersion->version()->associate($version);

            /** @var \App\Models\Installs\Install */
            $install = app('install.ongoing');
            if ($install) {
                $instanceVersion->install()->associate($install);
            }

            $instanceVersion->save();

            $this->set('instance_version_id', $instanceVersion->id);
        });
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Clearing saved data if found...');
        $this->try(function () {
            $instanceVersionId = $this->get('instance_version_id', required: false);
            if ($instanceVersionId) {
                $this->log(StageLogType::INFO, sprintf('Found instance version record #%d! Deleting...', $instanceVersionId));

                InstanceVersion::where('id', '=', $instanceVersionId)->delete();

                $this->log(StageLogType::INFO, 'Successfully deleted database record!');
            }
        }, throw: false);
    }
}
