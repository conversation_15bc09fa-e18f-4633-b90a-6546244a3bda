<?php

namespace App\System\Installs\Stages\Implementations\Filesystem\Support;

use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\Traits\RequiresSshExec;
use InvalidArgumentException;

class CopyFile
{
    use RequiresSshExec;

    public function __construct()
    {
        //
    }

    public function copy(string $filename, string $copy): void
    {
        if ($filename === $copy) {
            throw new InvalidArgumentException('Original and copy filenames must be different!');
        }

        $copy = new SshCommand("cp $filename $copy");

        app('command.hub')->execute($copy, $this->sshExec);
    }
}
