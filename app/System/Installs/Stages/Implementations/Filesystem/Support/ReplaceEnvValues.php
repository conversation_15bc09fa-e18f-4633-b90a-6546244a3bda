<?php

namespace App\System\Installs\Stages\Implementations\Filesystem\Support;

use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\Traits\RequiresSshExec;

class ReplaceEnvValues
{
    use RequiresSshExec;

    public function __construct()
    {
        //
    }

    public function replace(string $filename, array $keyValues, bool $escape): void
    {
        foreach ($keyValues as $key => $value) {
            $escapedValue = str_replace('/', '\\/', $value);
            $replacement = $escape ? "\"$escapedValue\"" : $escapedValue;

            $command = sprintf(
                'sed -i "s|^%s=.*$|%s=%s|g" %s',
                $key,
                $key,
                $replacement,
                $filename
            );

            $sed = new SshCommand($command);
            app('command.hub')->execute($sed, $this->sshExec);
        }
    }
}
