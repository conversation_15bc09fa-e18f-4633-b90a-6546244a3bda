<?php

namespace App\System\Installs\Stages\Implementations\Filesystem\Support;

use App\System\Installs\Stages\Generators\Traits\FileHelpers;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Execs\Traits\RequiresSshExec;

class WriteToFile
{
    use RequiresSshExec;
    use FileHelpers;

    public function __construct()
    {
        //
    }

    public function write(string $filename, string $contents, bool $append = false): void
    {
        $operator = $append ? '>>' : '>';

        $echo = new SshCommand(sprintf(
            "echo -e '%s' %s %s",
            $this->echoEscape($contents, true),
            $operator,
            $filename,
        ));

        app('command.hub')->execute($echo, $this->sshExec);
    }
}
