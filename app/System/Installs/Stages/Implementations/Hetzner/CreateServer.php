<?php

namespace App\System\Installs\Stages\Implementations\Hetzner;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Location;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingServerException;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Firewalls;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Locations;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Networks;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Servers;
use App\System\Installs\Stages\Implementations\Hetzner\Support\SshKeys;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use RuntimeException;

/**
 * READS:
 *  server_ssh_key_names - array of SSH key names on Hetzner Cloud account to use for the server (optional).
 *  server_location_city - Hetzner location to use for the server.
 *  server_firewall_names - array of firewall names on Hetzner Cloud account to apply to the server.
 *  server_network_names - array of network names on Hetzner Cloud account to apply to the server.
 *  ssh_user - name of SSH user to use for connecting to the server (optional).
 *  private_key - private key for connection to the server via SSH (optional).
 *  server_name - name of new server on Hetzner Cloud.
 *  server_image - image to use for new server on Hetzner Cloud.
 *  server_type - type of server to use for new server on Hetzner Cloud.
 *
 * WRITES:
 *  server_id - server ID on Hetzner Cloud.
 *  elasticsearch_server_id - existing server ID containing ElasticSearch service on Hetzner Cloud.
 *  server_public_ip - server public IP.
 *  server_private_ip - server private IP.
 */
class CreateServer implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    private string $defaultServerCountry = 'DE';

    public function __construct()
    {
        //
    }

    public function run(): void
    {
        $this->logCommandResultsToStage();

        /**
         * Preparing SSH keys.
         */
        $this->log(StageLogType::INFO, 'Collecting SSH ids...');
        $sshKeyIds = $this->try(function () {
            $sshKeyNames = $this->get('server_ssh_key_names', required: false) ?: [];
            $sshKeyIds = [];
            foreach ($sshKeyNames as $name) {
                $sshKey = (new SshKeys())->getSshKeyByName($name);
                $sshKeyIds[] = $sshKey->id;
            }

            return $sshKeyIds;
        });

        $additional = [
            'ssh_keys' => $sshKeyIds,
        ];

        /**
         * Selecting server location.
         */
        $locationId = $this->try(function () {
            $city = $this->get('server_location_city', required: false);

            return $this->getLocationByName($city)?->id;
        });
        if (empty($locationId)) {
            $this->log(StageLogType::ERROR, 'No acceptable server location found!');

            throw new RuntimeException('No acceptable server location found!');
        }
        $additional['location'] = $locationId;

        /**
         * Preparing firewalls.
         */
        $firewallNames = $this->get('server_firewall_names', required: false) ?: [];
        if (! empty($firewallNames)) {
            $this->log(StageLogType::INFO, 'Collecting firewall ids...');

            $firewallIds = $this->try(function () use ($firewallNames) {
                $firewallIds = [];
                foreach ($firewallNames as $name) {
                    $firewall = (new Firewalls())->getFirewallByName($name);
                    $firewallIds[] = $firewall->id;
                }

                return $firewallIds;
            });

            $additional['firewalls'] = collect($firewallIds)->map(fn ($id) => ['firewall' => $id])->all();
        }

        /**
         * Preparing networks.
         */
        $networkNames = $this->get('server_network_names', required: false) ?: [];
        if (! empty($networkNames)) {
            $this->log(StageLogType::INFO, 'Collecting network ids...');

            $networkIds = $this->try(function () use ($networkNames) {
                $networkIds = [];
                foreach ($networkNames as $name) {
                    $network = (new Networks())->getNetworkByName($name);
                    $networkIds[] = $network->id;
                }

                return $networkIds;
            });
            $additional['networks'] = $networkIds;
        }

        /**
         * Creating server.
         */
        $this->log(StageLogType::INFO, 'Sending request to create server via Hetzner Cloud API...');
        $serversApi = new Servers();
        /** @var \App\System\Installs\Stages\Implementations\Hetzner\Data\Server */
        $server = $this->try(function () use ($additional, $serversApi) {
            [$name, $image, $type] = $this->getServerAttributesFromContext();

            return $serversApi->createServer($name, $image, $type, $additional);
        });

        $this->set('server_id', $server->id);
        $this->set('server_public_ip', $server->publicIp);
        $this->set('server_private_ip', $server->privateIp);
        $this->set('server_name', $server->name);

        $this->log(StageLogType::INFO, 'Waiting for initialization to finish...');

        /**
         * Waiting for server to start.
         */
        $idleTime = 0;
        while (true) {
            // Waiting no more than 5 minutes for server to finish initialization.
            if ($idleTime > 300) {
                throw new RuntimeException('Max wait time for server initialization has passed. Aborting');
            }

            /** @var \App\System\Installs\Stages\Implementations\Hetzner\Data\Server */
            $serverUpdated = $this->try(function () use ($server, $serversApi) {
                return $this->runSilenced(function () use ($server, $serversApi) {
                    return $serversApi->getServerById($server->id);
                });
            });

            // Waiting until server is successfully running and is connected to private network.
            if ($serverUpdated->status === 'running' && ! empty($serverUpdated->privateIp)) {
                break;
            }

            $idleTime += 5;
            sleep(5);
        }

        $this->log(StageLogType::INFO, sprintf('Successfully created server #%d!', $server->id), $server->data);
        $this->log(StageLogType::INFO, 'Waiting for additional internal server setup...');
        sleep(5);
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Rolling back created server instance...');
        $serverId = $this->get('server_id', required: false);
        if (empty($serverId)) {
            $this->log(StageLogType::INFO, 'No local created server record found, nothing to do.');

            return;
        }

        $serversApi = new Servers();

        $this->log(
            StageLogType::INFO,
            sprintf('Found local created server record with ID "%d", verifying on Hetzner Cloud...', $serverId),
        );

        $serverExists = $this->try(function () use ($serversApi, $serverId) {
            try {
                // Making sure server instance exists.
                $serversApi->getServerById($serverId);

                return true;
            } catch (MissingServerException) {
                $this->log(StageLogType::INFO, 'No Hetzner Cloud server record found, nothing to do.');

                return false;
            }
        }, throw: false);

        if (! $serverExists) {
            return;
        }

        $this->log(StageLogType::INFO, 'Deleting Hetzner Cloud server record...');
        $this->try(function () use ($serversApi, $serverId) {
            $serversApi->deleteServerById($serverId);
        }, throw: false);

        $this->log(StageLogType::INFO, sprintf('Successfully deleted server #%d!', $serverId));
    }

    private function getServerAttributesFromContext(): array
    {
        $name = $this->get('server_name');
        $image = $this->get('server_image');
        $type = $this->get('server_type');

        return [$name, $image, $type];
    }

    private function getLocationByName(string|null $name): Location|null
    {
        $result = null;

        $this->log(StageLogType::INFO, 'Resolving server location...');
        $locations = (new Locations())->getAllLocations();

        // Search by name if provided.
        if (! empty($name)) {
            $result = collect($locations)->first(fn (Location $location) => $location->city === $name);

            if (empty($result)) {
                $this->log(StageLogType::WARNING, sprintf(
                    'No location by the name "%s" was found!',
                    $name,
                ));
            } else {
                $this->log(StageLogType::INFO, sprintf(
                    'Using location at city "%s".',
                    $result->city,
                    $this->defaultServerCountry,
                ));
            }
        }

        // Try to select first location from default country if previous searches failed.
        if (empty($result)) {
            $this->log(StageLogType::INFO, sprintf(
                'Trying to use location from default country "%s"...',
                $this->defaultServerCountry,
            ));

            $result = collect($locations)->first(fn (Location $location) => $location->country === 'DE');
            if (empty($result)) {
                $this->log(StageLogType::WARNING, sprintf(
                    'No suitable location found from default country "%s"!',
                    $this->defaultServerCountry,
                ));
            } else {
                $this->log(StageLogType::INFO, sprintf(
                    'Using location at city "%s" from default country "%s".',
                    $result->city,
                    $this->defaultServerCountry,
                ));
            }
        }

        return $result;
    }
}
