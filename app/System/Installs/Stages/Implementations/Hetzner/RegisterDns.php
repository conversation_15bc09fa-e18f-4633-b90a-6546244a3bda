<?php

namespace App\System\Installs\Stages\Implementations\Hetzner;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\RecordType;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\Dns\MissingZoneException;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Dns\Records;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Dns\Zones;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use Throwable;

/**
 * READS:
 *  zone_name - name of the domain zone to create new DNS record in.
 *  subdomain - DNS record's subdomain name in that zone.
 *  ip - IP to set for this new DNS record.
 * WRITES:
 *  record_id - ID of new DNS record.
 */
class RegisterDns implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function __construct()
    {
        //
    }

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Collecting network info for DNS records...');
        $zoneId = $this->try(function () {
            try {
                $zonesApi = new Zones();
                $zoneName = $this->get('zone_name');

                $zone = $zonesApi->getZoneByName($zoneName);
            } catch (MissingZoneException $e) {
                // In case zone is missing then we must create one.
                $zone = $zonesApi->createZone($zoneName, 86400);
            }

            return $zone->id;
        });

        $this->log(StageLogType::INFO, 'Creating DNS record for server...');
        $record = $this->try(function () use ($zoneId) {
            return (new Records())->createRecord(
                $this->get('subdomain'),
                300,
                RecordType::A,
                $this->get('ip'),
                $zoneId,
            );
        });
        $this->set('record_id', $record->id);
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Rolling back registered DNS records...');
        $recordIds = [
            $this->get('record_id', required: false),
        ];

        if (empty($recordIds)) {
            $this->log(StageLogType::INFO, 'No registered DNS records found, nothing to do.');

            return;
        }

        foreach ($recordIds as $recordId) {
            $this->try(function () use ($recordId) {
                $this->log(
                    StageLogType::INFO,
                    sprintf('Found registered DNS record with ID "%s", deleting on Hetzner DNS...', $recordId),
                );

                $records = new Records();

                try {
                    $records->deleteRecordById($recordId);

                    $this->log(
                        StageLogType::INFO,
                        sprintf('Successfully deleted DNS record with ID "%s"!', $recordId),
                    );
                } catch (Throwable $e) {
                    $this->log(StageLogType::WARNING, sprintf(
                        'Error deleting DNS record "%s"! Error: %s.',
                        $recordId,
                        $e->getMessage(),
                    ));
                }
            }, throw: false);
        }
    }
}
