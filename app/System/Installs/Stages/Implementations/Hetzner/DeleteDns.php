<?php

namespace App\System\Installs\Stages\Implementations\Hetzner;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Dns\Records;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use Throwable;

class DeleteDns implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Deleting registered DNS records...');

        $recordId = $this->get('dns.record_id', required: false);

        if (empty($recordId)) {
            $this->log(StageLogType::INFO, 'No registered DNS record found, nothing to do.');

            return;
        }

        $this->try(function () use ($recordId) {
            $this->log(
                StageLogType::INFO,
                sprintf('Found registered DNS record with ID "%s", deleting on Hetzner DNS...', $recordId),
            );

            $records = new Records();

            try {
                $records->deleteRecordById($recordId);

                $this->log(
                    StageLogType::INFO,
                    sprintf('Successfully deleted DNS record with ID "%s"!', $recordId),
                );
            } catch (Throwable $e) {
                $this->log(StageLogType::WARNING, sprintf(
                    'Error deleting DNS record "%s"! Error: %s.',
                    $recordId,
                    $e->getMessage(),
                ));
            }
        });
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
