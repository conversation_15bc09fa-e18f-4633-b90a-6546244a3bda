<?php

namespace App\System\Installs\Stages\Implementations\Hetzner;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingServerException;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Servers;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;

class DeleteServer implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $serverId = $this->get('server_id', required: false);
        if (empty($serverId)) {
            $this->log(StageLogType::INFO, 'No local created server record found, nothing to do.');

            return;
        }

        $serversApi = new Servers();

        $this->log(
            StageLogType::INFO,
            sprintf('Found local created server record with ID "%d", verifying on Hetzner Cloud...', $serverId),
        );

        $serverExists = $this->try(function () use ($serversApi, $serverId) {
            try {
                // Making sure server instance exists.
                $serversApi->getServerById($serverId);

                return true;
            } catch (MissingServerException) {
                $this->log(StageLogType::INFO, 'No Hetzner Cloud server record found, nothing to do.');

                return false;
            }
        }, throw: false);

        if (! $serverExists) {
            return;
        }

        $this->log(StageLogType::INFO, 'Deleting Hetzner Cloud server record...');
        $this->try(function () use ($serversApi, $serverId) {
            $serversApi->deleteServerById($serverId);
        });

        $this->log(StageLogType::INFO, sprintf('Successfully deleted server #%d!', $serverId));
    }

    public function rollback(): void
    {
        $this->log(StageLogType::WARNING, 'Rollback not supported!');
    }
}
