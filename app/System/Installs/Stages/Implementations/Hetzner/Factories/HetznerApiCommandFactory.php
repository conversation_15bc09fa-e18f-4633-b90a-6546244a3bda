<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Factories;

use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\HetznerDnsApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Runtime\Commands\ApiCommand;

class HetznerApiCommandFactory
{
    public function createForCloud(HetznerApiAction $action, array $data = [], array $routeParams = []): ApiCommand
    {
        $headers = [];
        if ($action->requiresAuth()) {
            $headers['Authorization'] = 'Bearer ' . config('services.hetzner.cloud.api_token');
        }

        return app(ApiCommand::class, [
            'url' => config('services.hetzner.cloud.url') . $action->route($routeParams),
            'method' => $action->apiMethod(),
            'data' => $data,
            'headers' => $headers,
        ]);
    }

    public function createForDns(HetznerDnsApiAction $action, array $data = [], array $routeParams = []): ApiCommand
    {
        $headers = [];
        if ($action->requiresAuth()) {
            $headers['Auth-API-Token'] = config('services.hetzner.dns.api_token');
        }

        return app(ApiCommand::class, [
            'url' => config('services.hetzner.dns.url') . $action->route($routeParams),
            'method' => $action->apiMethod(),
            'data' => $data,
            'headers' => $headers,
        ]);
    }
}
