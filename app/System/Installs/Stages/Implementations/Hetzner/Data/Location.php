<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data;

class Location
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $description,
        public readonly string $city,
        public readonly string $country,
        public readonly string $networkZone,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['id'],
            $data['name'],
            $data['description'],
            $data['city'],
            $data['country'],
            $data['network_zone'],
        );
    }
}
