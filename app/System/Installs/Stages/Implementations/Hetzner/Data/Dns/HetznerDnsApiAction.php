<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data\Dns;

use App\System\Runtime\Commands\Data\ApiMethod;
use LogicException;

enum HetznerDnsApiAction: string
{
    // Zones.
    case GET_ALL_ZONES = 'get_all_zones';
    case CREATE_ZONE = 'create_zone';
    // Records.
    case CREATE_RECORD = 'create_record';
    case DELETE_RECORD = 'delete_record';

    public function requiresAuth(): bool
    {
        return true;
    }

    public function route(array $params = []): string
    {
        return match ($this) {
            // Zones.
            self::GET_ALL_ZONES => '/v1/zones',
            self::CREATE_ZONE => '/v1/zones',
            // Records.
            self::CREATE_RECORD => '/v1/records',
            self::DELETE_RECORD => template('/v1/records/:id', $params),

            default => throw new LogicException(sprintf('Missing route for Hetzner DNS API action "%s"!', $this->value)),
        };
    }

    public function apiMethod(): ApiMethod
    {
        return match ($this) {
            // Zones.
            self::GET_ALL_ZONES => ApiMethod::GET,
            self::CREATE_ZONE => ApiMethod::POST,
            // Records.
            self::CREATE_RECORD => ApiMethod::POST,
            self::DELETE_RECORD => ApiMethod::DELETE,

            default => throw new LogicException(sprintf('Missing API method for Hetzner DNS API action "%s"!', $this->value)),
        };
    }
}
