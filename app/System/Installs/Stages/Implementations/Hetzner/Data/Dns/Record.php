<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data\Dns;

class Record
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $value,
        public readonly RecordType $type,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['id'],
            $data['name'],
            $data['value'],
            RecordType::from($data['type']),
        );
    }
}
