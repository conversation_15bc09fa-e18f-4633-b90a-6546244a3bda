<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data\Dns;

class Zone
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['id'],
            $data['name'],
        );
    }
}
