<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data;

use App\System\Runtime\Commands\Data\ApiMethod;
use LogicException;

enum HetznerApiAction: string
{
    // Servers.
    case CREATE_SERVER = 'create_server';
    case GET_SERVER = 'get_server';
    case GET_ALL_SERVERS = 'get_all_servers';
    case DELETE_SERVER = 'delete_server';
    // Firewalls.
    case CREATE_FIREWALL = 'create_firewall';
    case GET_ALL_FIREWALLS = 'get_all_firewalls';
    case DELETE_FIREWALL = 'delete_firewall';
    case REMOVE_FIREWALL_FROM_RESOURCES = 'remove_firewall_from_resources';
    // Networks.
    case GET_ALL_NETWORKS = 'get_all_networks';
    // Locations.
    case GET_ALL_LOCATIONS = 'get_all_locations';
    // SSH Keys.
    case GET_ALL_SSH_KEYS = 'get_all_ssh_keys';

    public function requiresAuth(): bool
    {
        return true;
    }

    public function route(array $params = []): string
    {
        return match ($this) {
            // Servers.
            self::CREATE_SERVER => '/v1/servers',
            self::GET_SERVER => template('/v1/servers/:id', $params),
            self::GET_ALL_SERVERS => '/v1/servers',
            self::DELETE_SERVER => template('/v1/servers/:id', $params),
            // Firewalls.
            self::CREATE_FIREWALL => '/v1/firewalls',
            self::GET_ALL_FIREWALLS => '/v1/firewalls',
            self::DELETE_FIREWALL => template('/v1/firewalls/:id', $params),
            self::REMOVE_FIREWALL_FROM_RESOURCES => template('/v1/firewalls/:id/actions/remove_from_resources', $params),
            // Networks.
            self::GET_ALL_NETWORKS => '/v1/networks',
            // Locations.
            self::GET_ALL_LOCATIONS => '/v1/locations',
            // SSH keys.
            self::GET_ALL_SSH_KEYS => '/v1/ssh_keys',

            default => throw new LogicException(sprintf('Missing route for Hetzner API action "%s"!', $this->value)),
        };
    }

    public function apiMethod(): ApiMethod
    {
        return match ($this) {
            // Servers.
            self::CREATE_SERVER => ApiMethod::POST,
            self::GET_SERVER => ApiMethod::GET,
            self::GET_ALL_SERVERS => ApiMethod::GET,
            self::DELETE_SERVER => ApiMethod::DELETE,
            // Firewalls.
            self::CREATE_FIREWALL => ApiMethod::POST,
            self::GET_ALL_FIREWALLS => ApiMethod::GET,
            self::DELETE_FIREWALL => ApiMethod::DELETE,
            self::REMOVE_FIREWALL_FROM_RESOURCES => ApiMethod::POST,
            // Networks.
            self::GET_ALL_NETWORKS => ApiMethod::GET,
            // Locations.
            self::GET_ALL_LOCATIONS => ApiMethod::GET,
            // SSH keys.
            self::GET_ALL_SSH_KEYS => ApiMethod::GET,

            default => throw new LogicException(sprintf('Missing API method for Hetzner API action "%s"!', $this->value)),
        };
    }
}
