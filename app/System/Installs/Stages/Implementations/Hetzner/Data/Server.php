<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Data;

use Illuminate\Support\Arr;

class Server
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $status,
        public readonly string $publicIp,
        public readonly string|null $privateIp,
        public readonly array $data,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['id'],
            $data['name'],
            $data['status'],
            publicIp: Arr::get($data, 'public_net.ipv4.ip'),
            privateIp: Arr::get($data, 'private_net.0.ip'),
            data: $data,
        );
    }
}
