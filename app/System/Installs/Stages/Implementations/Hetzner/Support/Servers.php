<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support;

use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Server;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingServerException;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use App\System\Runtime\Exceptions\CommandFailedException;
use Illuminate\Support\Arr;

class Servers
{
    public function createServer(string $name, string $image, string $serverType, array $additional): Server
    {
        $createServer = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::CREATE_SERVER,
            array_merge($additional, [
                'name' => $name,
                'image' => $image,
                'server_type' => $serverType,
            ]),
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createServer);

        $serverData = Arr::get($result->payload, 'server');

        return Server::createFromData($serverData);
    }

    public function getServerByName(string $name): Server
    {
        $getServers = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_ALL_SERVERS,
            ['name' => $name],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($getServers);

        $serverData = Arr::get($result->payload, 'servers.0');

        if (empty($serverData)) {
            throw new MissingServerException('name', $name);
        }

        return Server::createFromData($serverData);
    }

    public function getServerById(int $id): Server
    {
        $getServer = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_SERVER,
            routeParams: ['id' => $id],
        );

        try {
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $result = app('command.hub')->execute($getServer);
        } catch (CommandFailedException $e) {
            if ($e->error->code === 404) {
                throw new MissingServerException('id', $id);
            }

            throw $e;
        }

        $serverData = Arr::get($result->payload, 'server');

        return Server::createFromData($serverData);
    }

    public function deleteServerById(int $id): void
    {
        $deleteServer = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::DELETE_SERVER,
            routeParams: ['id' => $id],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        app('command.hub')->execute($deleteServer);
    }
}
