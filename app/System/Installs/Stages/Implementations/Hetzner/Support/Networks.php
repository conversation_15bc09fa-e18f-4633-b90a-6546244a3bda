<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support;

use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Network;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingNetworkException;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use Illuminate\Support\Arr;

class Networks
{
    public function getNetworkByName(string $name): Network
    {
        $getNetworks = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_ALL_NETWORKS,
            ['name' => $name],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($getNetworks);

        $networkData = Arr::get($result->payload, 'networks.0');

        if (empty($networkData)) {
            throw new MissingNetworkException($name);
        }

        return new Network(
            $networkData['id'],
            $networkData['name'],
        );
    }
}
