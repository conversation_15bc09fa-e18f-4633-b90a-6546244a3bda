<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support;

use App\System\Installs\Stages\Implementations\Hetzner\Data\Firewall;
use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Server;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingFirewallException;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use Illuminate\Support\Arr;

class Firewalls
{
    public function createFirewall(string $name, Server $server, array $rules, array $additional): Firewall
    {
        $createFirewall = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::CREATE_FIREWALL,
            array_merge($additional, [
                'name' => $name,
                'apply_to' => [
                    [
                        'server' => [
                            'id' => $server->id,
                        ],
                        'type' => 'server',
                    ],
                ],
                'rules' => $rules,
            ]),
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createFirewall);

        $firewallData = Arr::get($result->payload, 'firewall');

        return new Firewall(
            $firewallData['id'],
            $firewallData['name'],
        );
    }

    public function getFirewallByName(string $name): Firewall
    {
        $getFirewalls = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_ALL_FIREWALLS,
            ['name' => $name],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($getFirewalls);

        $firewallData = Arr::get($result->payload, 'firewalls.0');

        if (empty($firewallData)) {
            throw new MissingFirewallException($name);
        }

        return new Firewall(
            $firewallData['id'],
            $firewallData['name'],
        );
    }

    public function deleteFirewallById($id): void
    {
        $deleteFirewall = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::DELETE_FIREWALL,
            routeParams: ['id' => $id],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        app('command.hub')->execute($deleteFirewall);
    }

    public function removeFirewallFromServersById($firewallId, array $serverIds): void
    {
        $removeFirewallFromServer = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::REMOVE_FIREWALL_FROM_RESOURCES,
            data: [
                'remove_from' => collect($serverIds)->map(fn ($serverId) => [
                    'server' => [
                        'id' => $serverId,
                    ],
                    'type' => 'server',
                ])->all(),
            ],
            routeParams: ['id' => $firewallId],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        app('command.hub')->execute($removeFirewallFromServer);
    }
}
