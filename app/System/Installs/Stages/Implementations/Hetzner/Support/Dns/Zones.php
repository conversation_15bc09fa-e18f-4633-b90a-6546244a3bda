<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support\Dns;

use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\HetznerDnsApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\Zone;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\Dns\MissingZoneException;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use App\System\Runtime\Exceptions\CommandFailedException;
use Illuminate\Support\Arr;

class Zones
{
    public function createZone(string $name, int $ttl): Zone
    {
        $createZone = (new HetznerApiCommandFactory())->createForDns(
            HetznerDnsApiAction::CREATE_ZONE,
            [
                'name' => $name,
                'ttl' => $ttl,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createZone);

        $zoneData = Arr::get($result->payload, 'zone');

        return Zone::createFromData($zoneData);
    }

    public function getZoneByName(string $name): Zone
    {
        $getZones = (new HetznerApiCommandFactory())->createForDns(
            HetznerDnsApiAction::GET_ALL_ZONES,
            ['name' => $name],
        );

        try {
            /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
            $result = app('command.hub')->execute($getZones);
        } catch (CommandFailedException $e) {
            if ($e->error->code === 404) {
                throw new MissingZoneException($name);
            }

            throw $e;
        }

        $zoneData = Arr::get($result->payload, 'zones.0');

        if (empty($zoneData)) {
            throw new MissingZoneException($name);
        }

        return new Zone(
            $zoneData['id'],
            $zoneData['name'],
        );
    }
}
