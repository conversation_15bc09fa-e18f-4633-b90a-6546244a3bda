<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support\Dns;

use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\HetznerDnsApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\Record;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Dns\RecordType;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use Illuminate\Support\Arr;

class Records
{
    public function createRecord(string $name, int $ttl, RecordType $type, string $value, string $zoneId): Record
    {
        $createRecord = (new HetznerApiCommandFactory())->createForDns(
            HetznerDnsApiAction::CREATE_RECORD,
            [
                'name' => $name,
                'ttl' => $ttl,
                'type' => $type->value,
                'value' => $value,
                'zone_id' => $zoneId,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createRecord);

        $recordData = Arr::get($result->payload, 'record');

        return Record::createFromData($recordData);
    }

    public function deleteRecordById(string $id): void
    {
        $deleteRecord = (new HetznerApiCommandFactory())->createForDns(
            HetznerDnsApiAction::DELETE_RECORD,
            routeParams: ['id' => $id],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        app('command.hub')->execute($deleteRecord);
    }
}
