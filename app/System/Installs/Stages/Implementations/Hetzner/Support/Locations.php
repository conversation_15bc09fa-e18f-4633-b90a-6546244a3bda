<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support;

use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\Location;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use Illuminate\Support\Arr;

class Locations
{
    public function getAllLocations(): array
    {
        $getLocations = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_ALL_LOCATIONS,
            [],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($getLocations);

        $locationsData = Arr::get($result->payload, 'locations');

        if (empty($locationsData)) {
            return [];
        }

        return collect($locationsData)->map(fn ($data) => Location::createFromData($data))->all();
    }
}
