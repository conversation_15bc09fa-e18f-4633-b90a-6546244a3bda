<?php

namespace App\System\Installs\Stages\Implementations\Hetzner\Support;

use App\System\Installs\Stages\Implementations\Hetzner\Data\HetznerApiAction;
use App\System\Installs\Stages\Implementations\Hetzner\Data\SshKey;
use App\System\Installs\Stages\Implementations\Hetzner\Exceptions\MissingSshKeyException;
use App\System\Installs\Stages\Implementations\Hetzner\Factories\HetznerApiCommandFactory;
use Illuminate\Support\Arr;

class SshKeys
{
    public function getSshKeyByName(string $name): SshKey
    {
        $getSshKeys = (new HetznerApiCommandFactory())->createForCloud(
            HetznerApiAction::GET_ALL_SSH_KEYS,
            ['name' => $name],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($getSshKeys);

        $sshKeyData = Arr::get($result->payload, 'ssh_keys.0');

        if (empty($sshKeyData)) {
            throw new MissingSshKeyException($name);
        }

        return new SshKey(
            $sshKeyData['id'],
            $sshKeyData['name'],
            $sshKeyData['fingerprint'],
        );
    }
}
