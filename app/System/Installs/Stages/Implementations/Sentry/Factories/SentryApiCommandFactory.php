<?php

namespace App\System\Installs\Stages\Implementations\Sentry\Factories;

use App\System\Installs\Stages\Implementations\Sentry\Data\SentryApiAction;
use App\System\Runtime\Commands\ApiCommand;

class SentryApiCommandFactory
{
    public function create(SentryApiAction $action, array $data = [], array $routeParams = []): ApiCommand
    {
        $headers = [];
        if ($action->requiresAuth()) {
            $headers['Authorization'] = 'Bearer ' . config('deploy.sentry.auth_token');
        }

        return app(ApiCommand::class, [
            'url' => config('deploy.sentry.url') . $action->route($routeParams),
            'method' => $action->apiMethod(),
            'data' => $data,
            'headers' => $headers,
        ]);
    }
}
