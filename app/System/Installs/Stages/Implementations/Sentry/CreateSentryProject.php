<?php

namespace App\System\Installs\Stages\Implementations\Sentry;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Installs\Stages\Implementations\Sentry\Data\ClientKey;
use App\System\Installs\Stages\Implementations\Sentry\Support\ClientKeys;
use App\System\Installs\Stages\Implementations\Sentry\Support\Projects;
use App\System\Installs\Stages\Traits\InteractsWithContext;
use App\System\Installs\Stages\Traits\InteractsWithStage;
use RuntimeException;

class CreateSentryProject implements StageImplementation, Contextable
{
    use InteractsWithStage;
    use InteractsWithContext;

    public function run(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Creating new sentry project...');
        /** @var \App\System\Installs\Stages\Implementations\Sentry\Data\Project */
        $project = $this->try(function () {
            return (new Projects())->createProject(
                $this->get('organization_slug'),
                $this->get('team_slug'),
                $this->get('project_name'),
                $this->get('platform', required: false, default: 'php'),
            );
        });

        $this->log(StageLogType::INFO, 'Retrieving default client key of this project...');
        /** @var \App\System\Installs\Stages\Implementations\Sentry\Data\ClientKey */
        $clientKey = $this->try(function () use ($project) {
            $keys = (new ClientKeys())->listProjectsClientKeys(
                $this->get('organization_slug'),
                $project->slug,
            );
            if (empty($keys)) {
                throw new RuntimeException('No client keys found for new sentry project!');
            }

            $default = collect($keys)->first(fn (ClientKey $key) => $key->name === 'Default');
            if (empty($default)) {
                throw new RuntimeException('New sentry project does not have a default key!');
            }

            return $default;
        });

        $this->log(StageLogType::INFO, 'Saving project and DSN info...');
        $this->try(function () use ($project, $clientKey) {
            $this->set('project_slug', $project->slug);
            $this->set('project_data', $project->data);
            $this->set('project_dsn', $clientKey->dsn);
            $this->set('project_client_key_data', $clientKey->data);
        });
    }

    public function rollback(): void
    {
        $this->logCommandResultsToStage();

        $this->log(StageLogType::INFO, 'Rolling back Sentry changes for this instance...');
        $this->try(function () {
            $projectSlug = $this->get('project_slug', required: false);
            if (empty($projectSlug)) {
                $this->log(StageLogType::INFO, 'No created Sentry project found. Nothing to do.');

                return;
            }

            $this->log(StageLogType::INFO, sprintf('Found Sentry project by slug "%s". Deleting...', $projectSlug));

            (new Projects())->deleteProject(
                $this->get('organization_slug'),
                $projectSlug,
            );

            $this->log(StageLogType::INFO, 'Sentry project successfully deleted!');
        }, throw: false);
    }
}
