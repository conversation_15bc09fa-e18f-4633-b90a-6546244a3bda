<?php

namespace App\System\Installs\Stages\Implementations\Sentry\Data;

use App\System\Runtime\Commands\Data\ApiMethod;
use LogicException;

enum SentryApiAction: string
{
    // Projects.
    case CREATE_PROJECT = 'create_project';
    case RETRIEVE_PROJECT = 'retrieve_project';
    case DELETE_PROJECT = 'delete_project';

    // Client Keys.
    case LIST_PROJECTS_CLIENT_KEYS = 'list_projects_client_keys';

    public function requiresAuth(): bool
    {
        return true;
    }

    public function route(array $params = []): string
    {
        return match ($this) {
            // Projects.
            self::CREATE_PROJECT => template('/api/0/teams/:organization_slug/:team_slug/projects/', $params),
            self::RETRIEVE_PROJECT => template('/api/0/projects/:organization_slug/:project_slug/', $params),
            self::DELETE_PROJECT => template('/api/0/projects/:organization_slug/:project_slug/', $params),
            // Client Keys.
            self::LIST_PROJECTS_CLIENT_KEYS => template('/api/0/projects/:organization_slug/:project_slug/keys/', $params),

            default => throw new LogicException(sprintf('Missing route for Sentry API action "%s"!', $this->value)),
        };
    }

    public function apiMethod(): ApiMethod
    {
        return match ($this) {
            // Servers.
            self::CREATE_PROJECT => ApiMethod::POST,
            self::RETRIEVE_PROJECT => ApiMethod::GET,
            self::DELETE_PROJECT => ApiMethod::DELETE,
            // Client Keys.
            self::LIST_PROJECTS_CLIENT_KEYS => ApiMethod::GET,

            default => throw new LogicException(sprintf('Missing API method for Sentry API action "%s"!', $this->value)),
        };
    }
}
