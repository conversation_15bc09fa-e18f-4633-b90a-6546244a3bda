<?php

namespace App\System\Installs\Stages\Implementations\Sentry\Data;

class ClientKey
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $dsn,
        public readonly array $data,
    ) {
        //
    }

    public static function createFromData(array|null $data): static|null
    {
        if (empty($data)) {
            return null;
        }

        return new static(
            $data['id'],
            $data['name'],
            $data['dsn']['public'],
            $data,
        );
    }
}
