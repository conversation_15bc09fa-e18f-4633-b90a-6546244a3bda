<?php

namespace App\System\Installs\Stages\Implementations\Sentry\Support;

use App\System\Installs\Stages\Implementations\Sentry\Data\ClientKey;
use App\System\Installs\Stages\Implementations\Sentry\Data\SentryApiAction;
use App\System\Installs\Stages\Implementations\Sentry\Factories\SentryApiCommandFactory;

class ClientKeys
{
    /**
     * @return \App\System\Installs\Stages\Implementations\Sentry\Data\ClientKey[]
     */
    public function listProjectsClientKeys(string $organizationSlug, string $projectSlug): array
    {
        $listProjectsClientKeys = (new SentryApiCommandFactory())->create(
            SentryApiAction::LIST_PROJECTS_CLIENT_KEYS,
            routeParams: [
                'organization_slug' => $organizationSlug,
                'project_slug' => $projectSlug,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($listProjectsClientKeys);

        return collect($result->payload)->map(fn ($data) => ClientKey::createFromData($data))->all();
    }
}
