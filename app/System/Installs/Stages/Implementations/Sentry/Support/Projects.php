<?php

namespace App\System\Installs\Stages\Implementations\Sentry\Support;

use App\System\Installs\Stages\Implementations\Sentry\Data\Project;
use App\System\Installs\Stages\Implementations\Sentry\Data\SentryApiAction;
use App\System\Installs\Stages\Implementations\Sentry\Factories\SentryApiCommandFactory;

class Projects
{
    public function createProject(
        string $organizationSlug,
        string $teamSlug,
        string $name,
        string|null $platform = null,
    ): Project {
        $createProject = (new SentryApiCommandFactory())->create(
            SentryApiAction::CREATE_PROJECT,
            data: [
                'name' => $name,
                'platform' => $platform,
            ],
            routeParams: [
                'organization_slug' => $organizationSlug,
                'team_slug' => $teamSlug,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($createProject);

        return Project::createFromData($result->payload);
    }

    public function retrieveProject(string $organizationSlug, string $projectSlug): Project
    {
        $retrieveProject = (new SentryApiCommandFactory())->create(
            SentryApiAction::RETRIEVE_PROJECT,
            routeParams: [
                'organization_slug' => $organizationSlug,
                'project_slug' => $projectSlug,
            ],
        );

        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
        $result = app('command.hub')->execute($retrieveProject);

        return Project::createFromData($result->payload);
    }

    public function deleteProject(string $organizationSlug, string $projectSlug): void
    {
        $deleteProject = (new SentryApiCommandFactory())->create(
            SentryApiAction::DELETE_PROJECT,
            routeParams: [
                'organization_slug' => $organizationSlug,
                'project_slug' => $projectSlug,
            ],
        );

        app('command.hub')->execute($deleteProject);
    }
}
