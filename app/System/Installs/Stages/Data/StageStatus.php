<?php

namespace App\System\Installs\Stages\Data;

enum StageStatus: string
{
    case PENDING = 'pending';
    case RUNNING = 'running';
    case FINISHED = 'finished';
    case FAILED = 'failed';
    case CANCELED = 'canceled';

    public function isFinal(): bool
    {
        switch ($this) {
            case self::FINISHED:
            case self::FAILED:
            case self::CANCELED:
                return true;
        }

        return false;
    }
}
