<?php

namespace App\System\Installs\Stages\Contracts;

use App\Models\Infrastructure\Group;
use App\System\Installs\Stages\Data\Stages;
use App\System\Runtime\Context;

interface StagesStrategy
{
    /**
     * Validate if stages strategy instance is properly initialized. Throw validation exception if not.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function assertItIsValid(): void;

    /**
     * Get list of stages for this strategy and their context.
     */
    public function getStages(): Stages;

    /**
     * Extract array of resulting values for this strategy from given context.
     */
    public function getResult(Context|null $context): array|null;

    /**
     * Return group associated with this strategy given the provided context.
     */
    public function getGroup(Context|null $context): Group|null;
}
