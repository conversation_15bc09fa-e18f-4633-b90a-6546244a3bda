<?php

namespace App\System\Installs\Stages\Contracts;

use App\Models\Installs\Stage;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Runtime\Context;

interface StageImplementation
{
    public function getClass(): string;

    public function provideName(string $name): static;
    public function getName(): string;

    public function provideContext(Context $context): static;

    public function provideScope(StageScope|null $scope): static;
    public function getScope(): StageScope|null;

    public function linkWithStage(Stage $stage): void;

    public function run(): void;
    public function rollback(): void;
}
