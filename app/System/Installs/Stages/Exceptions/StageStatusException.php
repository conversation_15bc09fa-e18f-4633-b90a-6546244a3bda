<?php

namespace App\System\Installs\Stages\Exceptions;

use App\Models\Installs\Stage;
use App\System\Installs\Stages\Data\StageStatus;
use Exception;

class StageStatusException extends Exception
{
    public function __construct(
        public readonly Stage $stage,
        public readonly StageStatus $mustBe,
    ) {
        parent::__construct(sprintf(
            'Stage #%d status must be "%s", but is "%s" instead!',
            $stage->id,
            $mustBe->value,
            $stage->status,
        ));
    }
}
