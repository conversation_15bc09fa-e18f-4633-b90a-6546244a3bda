<?php

namespace App\System\Installs\Stages\Factories;

use App\Models\Installs\Install;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Strategies\AddEnvelopeToOldDms;
use App\System\Installs\Stages\Strategies\BasicDmsSetupStrategy;
use App\System\Installs\Stages\Strategies\DeleteDmsInstanceStrategy;
use App\System\Installs\Stages\Strategies\PwaForDmsSetupStrategy;
use App\System\Installs\Stages\Strategies\UpgradeDmsInstancesLimitsStatsStrategy;
use App\System\Installs\Stages\Strategies\UpgradeDmsStrategy;
use App\System\Installs\Stages\Strategies\UpgradeDmsSubscriptionStrategy;
use App\System\Installs\Stages\Strategies\UpgradePwaStrategy;
use LogicException;

class StagesStrategyFactory
{
    public function createForInstall(Install $install): StagesStrategy
    {
        $strategy = match ($install->type) {
            // DMS.
            InstallType::UPGRADE_DMS => new UpgradeDmsStrategy($install->getInstallPayload()),
            InstallType::BASIC_DMS_SETUP => new BasicDmsSetupStrategy($install->getInstallPayload()),
            InstallType::DELETE_DMS_INSTANCE => new DeleteDmsInstanceStrategy($install->getInstallPayload()),
	        InstallType::UPGRADE_DMS_SUBSCRIPTION => new UpgradeDmsSubscriptionStrategy($install->getInstallPayload()),
	        InstallType::UPGRADE_DMS_INSTANCES_LIMITS_STATS => new UpgradeDmsInstancesLimitsStatsStrategy($install->getInstallPayload()),
            InstallType::ADD_ENVELOPE_TO_OLD_DMS => new AddEnvelopeToOldDms($install->getInstallPayload()),

            // PWA.
            InstallType::UPGRADE_PWA => new UpgradePwaStrategy($install->getInstallPayload()),
            InstallType::PWA_FOR_DMS_SETUP => new PwaForDmsSetupStrategy($install->getInstallPayload()),

            default => throw new LogicException(sprintf(
                'Missing stages strategy class for type "%s"!',
                $install->type->value,
            )),
        };

        return $strategy->provideInstall($install);
    }
}
