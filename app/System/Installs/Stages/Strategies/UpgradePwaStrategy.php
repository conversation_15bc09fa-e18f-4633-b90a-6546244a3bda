<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Implementations\Common\CheckUrlStatus;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStart;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStop;
use App\System\Installs\Stages\Implementations\Docker\UpdateDockerComposeConfigs;
use App\System\Installs\Stages\Implementations\Pwa\StopAndUpgradePwa;
use App\System\Installs\Stages\Implementations\Versions\AssignVersionToInstance;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class UpgradePwaStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;
    private Version|null $targetVersion = null;
    private SshConnection|null $sshConnection = null;

    public function __construct(private InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'pwa_id' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::PWA->value)],
            'target_version_id' => [
                'required',
                Rule::exists('versions', 'id')
                    ->where('app_type', AppType::PWA->value)
                    ->withoutTrashed(),
            ],
        ]);
        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['pwa_id']);
        }

        if (! $this->targetVersion) {
            $this->targetVersion = Version::where('id', '=', $this->payload->data['target_version_id'])
                ->where('app_type', AppType::PWA->value)
                ->firstOrFail();
        }

        $this->sshConnection = $this->instance->sshConnections->first();
        if (empty($this->sshConnection)) {
            throw ValidationException::withMessages([
                'pwa_id' => 'Given PWA ID is missing SSH connection required for PWA setup!',
            ]);
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $this->context = new Context();

        $stages = collect();
        $stages->push($this->getDockerComposeStopStage());
        $stages->push($this->getUpdateDockerComposeConfigsStage());
        $stages->push($this->getDockerComposeStartStage());
        $stages->push($this->getStopAndUpgradePwaStage());
        $stages->push($this->getWaitForPwaToBecomeAvailableStage());
        $stages->push($this->getAssignVersionToInstanceStage());

        return new Stages($stages->all(), $this->context);
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        return null;
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        return [
            //
        ];
    }

    private function getDockerComposeStopStage(): DockerComposeStop
    {
        $this->context->update([
            'docker_compose_stop' => [
                'instance_id' => $this->instance->id,
                'env_file' => 'pwa-docker-compose.env',
                'yaml_file' => 'pwa-docker-compose.yaml',
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Docker\DockerComposeStop */
        $dockerComposeStop = app(DockerComposeStop::class);
        $dockerComposeStop
            ->provideName('Stop running docker containers')
            ->provideContext($this->context)
            ->provideScope(new StageScope('docker_compose_stop'));

        return $dockerComposeStop;
    }

    private function getUpdateDockerComposeConfigsStage(): UpdateDockerComposeConfigs
    {
        $this->context->update([
            'update_configs' => [
                'instance_id' => $this->instance->id,
                'env_file' => 'pwa-docker-compose.env',
                'env_contents' => $this->targetVersion->docker_compose_env,
                'yaml_file' => 'pwa-docker-compose.yaml',
                'yaml_contents' => $this->targetVersion->docker_compose_yaml,
                'tag' => $this->targetVersion->tag,
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Docker\UpdateDockerComposeConfigs */
        $updateDockerComposeConfigs = app(UpdateDockerComposeConfigs::class);
        $updateDockerComposeConfigs
            ->provideName('Update docker compose configs')
            ->provideContext($this->context)
            ->provideScope(new StageScope('update_configs'));

        return $updateDockerComposeConfigs;
    }

    private function getDockerComposeStartStage(): DockerComposeStart
    {
        $this->context->update([
            'docker_compose_start' => [
                'instance_id' => $this->instance->id,
                'env_file' => 'pwa-docker-compose.env',
                'yaml_file' => 'pwa-docker-compose.yaml',
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Docker\DockerComposeStart */
        $dockerComposeStart = app(DockerComposeStart::class);
        $dockerComposeStart
            ->provideName('Fire up docker containers')
            ->provideContext($this->context)
            ->provideScope(new StageScope('docker_compose_start'));

        return $dockerComposeStart;
    }

    private function getStopAndUpgradePwaStage(): StopAndUpgradePwa
    {
        $this->context->update([
            'stop_and_upgrade_pwa' => [
                'instance_id' => $this->instance->id,
                'pwa_version' => $this->targetVersion->name,
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Pwa\StopAndUpgradePwa */
        $stopAndUpgrade = app(StopAndUpgradePwa::class);
        $stopAndUpgrade
            ->provideName('Stop PWA app, upgrade it internally and launch it back up')
            ->provideContext($this->context)
            ->provideScope(new StageScope('stop_and_upgrade_pwa'));

        return $stopAndUpgrade;
    }

    private function getWaitForPwaToBecomeAvailableStage(): CheckUrlStatus
    {
        /** @var \App\System\Infrastructure\Instances\Data\PwaInstanceData */
        $pwaData = $this->instance->data;

        $this->context->update([
            'check_pwa_availability' => [
                'url' => $pwaData->url,
                'number_of_tries' => 28, // 28 tries every 30 seconds = 840 seconds.
                'try_every' => 30,
                'check_ssl' => false,
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Common\CheckUrlStatus */
        $checkPwaAvailability = app(CheckUrlStatus::class);
        $checkPwaAvailability
            ->provideName('Waiting for PWA URL to be accessible again')
            ->provideContext($this->context)
            ->provideScope(new StageScope('check_pwa_availability'));

        return $checkPwaAvailability;
    }

    private function getAssignVersionToInstanceStage(): AssignVersionToInstance
    {
        $this->context->update([
            'assign_version_to_pwa' => [
                'instance_id' => $this->instance->id,
                'version_id' => $this->targetVersion->id,
            ],
        ]);

        /** @var  */
        $assignVersionToInstance = app(AssignVersionToInstance::class);
        $assignVersionToInstance
            ->provideName('Assigning new version relation for PWA')
            ->provideContext($this->context)
            ->provideScope(new StageScope('assign_version_to_pwa'));

        return $assignVersionToInstance;
    }
}
