<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\Models\Installs\Install;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeStart;
use App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport\RegisterEnvelopeApp;
use App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport\UpdateEnvFileEnvelopeValues;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use LogicException;

class AddEnvelopeToOldDms implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;

    public function __construct(private InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'dms_id' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::DMS)],
        ]);
        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['dms_id']);
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $this->context = new Context();

        $dmsInstallContext = $this->getInstallContext(InstallType::BASIC_DMS_SETUP);

        $stages = collect();

        $envelopeForDms = $dmsInstallContext->get('envelope_for_dms');
        if(empty($envelopeForDms)) {
            $subdomain = $dmsInstallContext->get('dms_docker_compose.subdomain');
            $url = $dmsInstallContext->get('dms_docker_compose.url');
            $stages->push($this->getRegisterEnvelopeAppStage($subdomain, $url));
            $stages->push($this->getUpdateEnvelopeEnvStage());
            $stages->push($this->getDockerComposeStartStage());
        }

        return new Stages($stages->all(), $this->context);
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        return [
            //
        ];
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        return $this->instance->group;
    }


    private function getInstallContext(InstallType $installType): Context
    {
        $install = Install::where('type', '=', $installType->value)
            ->where('group_id', '=', $this->instance->group->id)
            ->currentStatus(InstallStatus::FINISHED)
            ->first();

        if (empty($install)) {
            throw new LogicException(sprintf('No valid %s install was found!', $installType->value));
        }

        $installContext = new Context($install->resulting_context);

        return $installContext;
    }

    private function getRegisterEnvelopeAppStage(string $subdomain, string $url): RegisterEnvelopeApp
    {
        $this->context->update([
            'envelope_for_dms' => [
                'user' => config('deploy.envelope.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'host' => config('deploy.envelope.host'),
                'base_url' => config('deploy.envelope.url'),
                'app_name' => $subdomain,
                'app_url' => $url.'/en/channels',
                'webhook_url' => $url.'/api/envelope/v1/webhook',
                'container_name' => 'envelope-envelope-1'
            ],
        ]);

        $registerEnvelopeApp = new RegisterEnvelopeApp();
        $registerEnvelopeApp
            ->provideName('Register Envelope app for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('envelope_for_dms'));

        return $registerEnvelopeApp;
    }

    private function getUpdateEnvelopeEnvStage(): UpdateEnvFileEnvelopeValues
    {
        $this->context->update([
            'update_env_file' => [
                'instance_id' => $this->instance->id,
                'env_file' => 'docker-compose.env'
            ],
        ]);

        $stage = app(UpdateEnvFileEnvelopeValues::class);
        $stage
            ->provideName('Patch docker-compose.env with Envelope credentials')
            ->provideContext($this->context)
            ->provideScope(new StageScope('update_env_file', [
                'ENVELOPE_BASE_URL' => 'envelope_for_dms.base_url',
                'ENVELOPE_APP_ID' => 'envelope_for_dms.app_id',
                'ENVELOPE_AUTH_KEY_ID' => 'envelope_for_dms.auth_key_id',
                'ENVELOPE_AUTH_KEY_SECRET' => 'envelope_for_dms.auth_key_secret',
            ]));

        return $stage;
    }

    private function getDockerComposeStartStage(): DockerComposeStart
    {
        $this->context->update([
            'docker_compose_start' => [
                'instance_id' => $this->instance->id,
                'env_file' => 'docker-compose.env',
                'yaml_file' => 'docker-compose.yaml',
            ],
        ]);

        /** @var \App\System\Installs\Stages\Implementations\Docker\DockerComposeStart */
        $dockerComposeStart = app(DockerComposeStart::class);
        $dockerComposeStart
            ->provideName('Fire up docker containers')
            ->provideContext($this->context)
            ->provideScope(new StageScope('docker_compose_start'));

        return $dockerComposeStart;
    }

}
