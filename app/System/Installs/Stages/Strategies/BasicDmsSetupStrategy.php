<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Group;
use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Generators\Docker\DmsEnvFromContextGenerator;
use App\System\Installs\Stages\Generators\Docker\DmsYamlFromContextGenerator;
use App\System\Installs\Stages\Implementations\Common\GeneratePassword;
use App\System\Installs\Stages\Implementations\Dms\CreateDmsInstance;
use App\System\Installs\Stages\Implementations\Dms\SendDmsInvite;
use App\System\Installs\Stages\Implementations\Dms\WaitForDmsToDeploy;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeDeploy;
use App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport\RegisterEnvelopeApp;
use App\System\Installs\Stages\Implementations\Docker\RegisterAiServiceCredentials;
use App\System\Installs\Stages\Implementations\Docker\RegisterCheckerCredentials;
use App\System\Installs\Stages\Implementations\Docker\RegisterScrmCredentials;
use App\System\Installs\Stages\Implementations\Docker\RegisterSubscription;
use App\System\Installs\Stages\Implementations\Hetzner\CreateServer;
use App\System\Installs\Stages\Implementations\Hetzner\RegisterDns;
use App\System\Installs\Stages\Implementations\Minio\CreateBucket;
use App\System\Installs\Stages\Implementations\Pwa\Support\PwaData;
use App\System\Installs\Stages\Implementations\Sentry\CreateSentryProject;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class BasicDmsSetupStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Version|null $targetVersion = null;

    public function __construct(
        private readonly InstallPayload $payload,
    ) {
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'subdomain' => ['required', 'regex:/^[a-z0-9\-]+$/'],
            'email' => ['required', 'email'],
            'company_name' => ['required'],
            'with_pwa' => ['nullable', 'boolean'],
            'target_version_id' => [
                'nullable',
                Rule::exists('versions', 'id')
                    ->where('app_type', AppType::DMS->value)
                    ->withoutTrashed(),
            ],
            'extra' => ['nullable', 'array'],
            'extra.id' => ['required', 'string'],

            'extra.defaultLanguage' => ['nullable', 'string'],

            'extra.systemFeaturesStats' => ['required', 'array'],
            'extra.systemFeaturesStats.*' => ['nullable', 'array'],
            'extra.systemFeaturesStats.*.id' => ['required', 'string'],
            'extra.systemFeaturesStats.*.type' => ['required', 'string'],
            'extra.systemFeaturesStats.*.title' => ['nullable', 'string'],
            'extra.systemFeaturesStats.*.total' => ['required', 'numeric'],
            'extra.systemFeaturesStats.*.units' => ['required', 'string'],

            'extra.serviceFeatures' => ['required', 'array'],
            'extra.serviceFeatures.*' => ['nullable', 'array'],
            'extra.serviceFeatures.*.id' => ['required', 'string'],
            'extra.serviceFeatures.*.title' => ['nullable', 'string'],
            'extra.serviceFeatures.*.type' => ['required', 'string'],

            'extra.subscription' => ['nullable', 'array'],
            'extra.subscription.id' => ['required', 'string'],
            'extra.subscription.status' => ['required', 'string'],
            'extra.subscription.nextPeriodStartAt' => ['nullable'],
            'extra.subscription.lastPeriodEndsAt' => ['nullable'],
            'extra.subscription.nextPaymentAttempsAt' => ['nullable'],
            'extra.subscription.expiresAt' => ['nullable'],
            'extra.subscription.gracePeriodExpiresAt' => ['nullable'],
            'extra.subscription.startsAt' => ['nullable'],
            'extra.subscription.endsAt' => ['nullable'],
            'extra.subscription.trialEndsAt' => ['nullable'],
            'extra.subscription.cancelledAt' => ['nullable'],
            'extra.subscription.cancelAtPeriodEnd' => ['required', 'boolean'],
            'extra.subscription.billingCycle' => ['required', 'string'],

            'extra.subscription.plan' => ['nullable', 'array'],
            'extra.subscription.plan.id' => ['required', 'string'],
            'extra.subscription.plan.number' => ['required', 'numeric'],
            'extra.subscription.plan.name' => ['required', 'string'],
            'extra.subscription.plan.status' => ['required', 'string'],
            'extra.subscription.plan.type' => ['required', 'string'],
            'extra.subscription.plan.price' => ['required', 'numeric'],
            'extra.subscription.plan.yearly_price' => ['required', 'numeric'],
            'extra.subscription.plan.trialPeriodDays' => ['nullable', 'numeric'],
            'extra.subscription.plan.isActive' => ['required', 'boolean'],
            'extra.subscription.plan.hasTrial' => ['required', 'boolean'],
            'extra.subscription.plan.startDate' => ['nullable', 'date'],
            'extra.subscription.plan.endDate' => ['nullable', 'date'],

            'extra.subscription.plan.currency' => ['nullable', 'array'],
            'extra.subscription.plan.currency.id' => ['required', 'string'],
            'extra.subscription.plan.currency.code' => ['required', 'string'],
            'extra.subscription.plan.currency.symbol' => ['required', 'string'],
            'extra.subscription.plan.currency.name' => ['required', 'string'],
            'extra.subscription.plan.currency.isActive' => ['required', 'boolean'],
        ]);

        $validator->validate();

        if (! empty($this->payload->data['target_version_id'])) {
            if (! $this->targetVersion) {
                $this->targetVersion = Version::where('id', '=', $this->payload->data['target_version_id'])
                     ->where('app_type', '=', AppType::DMS->value)
                     ->firstOrFail();
            }
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $subdomain = $this->payload->data['subdomain'];
        $hostname = sprintf('%s.%s', $subdomain, config('deploy.dms.zone_name'));
        $url = sprintf('https://%s', $hostname);
        $sentryDsn = config('deploy.dms.sentry.dsn');
        $serverName =  sprintf(
            config('services.hetzner.cloud.server_name_template'),
            $subdomain,
        );

        if (! empty(config('deploy.dms.aws.bucket'))) {
            $awsData = [
                'url' => config('deploy.dms.aws.url'),
                'bucket' => config('deploy.dms.aws.bucket'),
                'access_key_id' => config('deploy.dms.aws.access_key_id'),
                'access_secret_key' => config('deploy.dms.aws.access_secret_key'),
            ];
        } else {
            $awsData = null;
        }

        $email = $this->payload->data['email'];

        $this->context = new Context();

        $stages = collect();
        $stages->push($this->getCreateServerStage($serverName, $subdomain));
        $stages->push($this->getRegisterDnsStage($subdomain));
        $stages->push($this->getRegisterCheckerCredentialsStage($subdomain));
        $stages->push($this->getRegisterAiServiceCredentialsStage($subdomain));
        $stages->push($this->getRegisterScrmCredentialsStage($subdomain));
        $stages->push($this->getRegisterEnvelopeAppStage($subdomain, $url));
        $stages->push($this->getDmsPasswordsAndKeysStage());

        // If we don't have predetermined Sentry DSN then we include Sentry project creation stage.
        if (empty($sentryDsn)) {
            $stages->push($this->getCreateSentryProjectStage($subdomain));
        }

        // If we don't have predetermined AWS data then we include Minio bucket creation stage.
        if (empty($awsData)) {
            $stages->push($this->getCreateBucketStage($subdomain));
        }

        $stages->push($this->getDockerComposeDeployStage($sentryDsn, $serverName, $awsData, $hostname, $subdomain, $url));
        $stages->push($this->getWaitForDmsToDeployStage());
        $stages->push($this->getCreateDmsInstanceStage($hostname, $subdomain, $serverName, $url, $email));

        // If we don't need to install PWA afterward then we are sending email with invite to DMS right away.
        if (! ($this->payload->data['with_pwa'] ?? false)) {
            $stages->push($this->getSendDmsInviteStage());
        }

        if (
            ! empty($this->payload->data['extra']['subscription'])
            && ! empty($this->payload->data['extra']['systemFeaturesStats'])
            && ! empty($this->payload->data['extra']['serviceFeatures'])
        ) {
            $stages->push($this->getRegisterSubscriptionStage());
        }

        return new Stages($stages->all(), $this->context);
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        return [
            'dms' => [
                'id' => $context->get('create_dms_instance.instance_id'),
                'url' => $context->get('dms_docker_compose.url'),
                'name' => $context->get('dms_docker_compose.company_name'),
                'credentials' => [
                    'username' => $context->get('wait_for_dms_to_deploy.result_email'),
                    'email' => $context->get('wait_for_dms_to_deploy.result_email'),
                ],
                'api' => [
                    'client_id' => $context->get('wait_for_dms_to_deploy.result_hub_client_id'),
                    'client_secret' => $context->get('wait_for_dms_to_deploy.result_hub_client_secret'),
                ],

                // Additional SCRM compatible fields.
                'release_id' => $this->targetVersion?->id,
                'instance_release_id' => $this->install?->id,
            ],
        ];
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        $groupId = $context->get('create_dms_instance.group_id');

        return Group::withTrashed()->firstWhere('id', '=', $groupId);
    }

    private function getCreateServerStage(string $serverName, string $subdomain): CreateServer
    {
        $this->context->update([
            'create_hetzner_vm' => [
                'name' => $subdomain,

                'ssh_user' => config('deploy.dms.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),

                'server_name' => $serverName,
                'server_location_city' => 'Falkenstein',
                'server_image' => 'docker-ce',
                'server_type' => config('services.hetzner.cloud.server_type.dms'),
                'server_firewall_names' => config('services.hetzner.cloud.firewall_names'),
                'server_network_names' => config('services.hetzner.cloud.network_names'),
                'server_ssh_key_names' => config('services.hetzner.cloud.ssh_key_names'),
                'server_ssh_key_file' => config('services.hetzner.cloud.ssh_key_file'),
            ],
        ]);

        $createServer = new CreateServer();
        $createServer
            ->provideName('Create server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('create_hetzner_vm'));

        return $createServer;
    }

    private function getRegisterDnsStage(string $subdomain): RegisterDns
    {
        $this->context->update([
            'dns' => [
                'subdomain' => $subdomain,
                'zone_name' => config('deploy.dms.zone_name'),
            ],
        ]);

        $registerDns = new RegisterDns();
        $registerDns
            ->provideName('Register public DNS for server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('dns', ['ip' => 'create_hetzner_vm.server_public_ip']));

        return $registerDns;
    }

    private function getRegisterCheckerCredentialsStage(string $subdomain): RegisterCheckerCredentials
    {
        $this->context->update([
            'checker_for_dms' => [
                'user' => config('deploy.checker.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'host' => config('deploy.checker.host'),
                'base_url' => config('deploy.checker.url'),
                'app_name' => $subdomain,
                'container_name' => 'checker-checker-1',
            ],
        ]);

        $registerCheckerCredentials = new RegisterCheckerCredentials();
        $registerCheckerCredentials
            ->provideName('Create Checker API credentials for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('checker_for_dms'));

        return $registerCheckerCredentials;
    }

    private function getRegisterAiServiceCredentialsStage(string $subdomain): RegisterAiServiceCredentials
    {
        $this->context->update([
            'ai_service_for_dms' => [
                'user' => config('deploy.ai_service.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'host' => config('deploy.ai_service.host'),
                'base_url' => config('deploy.ai_service.url'),
                'app_name' => $subdomain,
                'container_name' => 'ai-service-adapter',
            ]
        ]);


        $registerAiServiceCredentials = new RegisterAiServiceCredentials();
        $registerAiServiceCredentials
            ->provideName('Create AI Service API credentials for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('ai_service_for_dms'));

        return $registerAiServiceCredentials;
    }

    private function getRegisterScrmCredentialsStage(string $subdomain): RegisterScrmCredentials
    {
        $this->context->update([
            'scrm_for_dms' => [
                'user' => config('deploy.scrm.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'host' => config('deploy.scrm.host'),
                'base_url' => config('deploy.scrm.url'),
                'app_name' => $subdomain,
                'container_name' => 'scrm-scrm-1',
                'grant_type' => 'client_credentials',
                'scope' => 'api_scrm_external',
                'instance_id' => $this->payload->data['extra']['id']
            ],
        ]);


        $registerScrmCredentials = new RegisterScrmCredentials();
        $registerScrmCredentials
            ->provideName('Register SCRM credentials for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('scrm_for_dms'));

        return $registerScrmCredentials;
    }

    private function getRegisterEnvelopeAppStage(string $subdomain, string $url): RegisterEnvelopeApp
    {
        $this->context->update([
            'envelope_for_dms' => [
                'user' => config('deploy.envelope.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'host' => config('deploy.envelope.host'),
                'base_url' => config('deploy.envelope.url'),
                'app_name' => $subdomain,
                'app_url' => $url.'/en/channels',
                'webhook_url' => $url.'/api/envelope/v1/webhook',
                'container_name' => 'envelope-envelope-1'
            ],
        ]);

        $registerEnvelopeApp = new RegisterEnvelopeApp();
        $registerEnvelopeApp
            ->provideName('Register Envelope app for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('envelope_for_dms'));

        return $registerEnvelopeApp;
    }
    private function getCreateSentryProjectStage(string $subdomain): CreateSentryProject
    {
        $this->context->update([
            'sentry_project_for_dms' => [
                'organization_slug' => config('deploy.sentry.organization_slug'),
                'team_slug' => config('deploy.sentry.team_slug'),
                'project_name' => sprintf(
                    config('deploy.sentry.dms_project_name_template'),
                    $subdomain,
                ),
                'platform' => 'php-laravel',
            ],
        ]);

        $createSentryProject = new CreateSentryProject();
        $createSentryProject
            ->provideName('Create Sentry project for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('sentry_project_for_dms', [
                //
            ]));

        return $createSentryProject;
    }

    private function getDmsPasswordsAndKeysStage(): GeneratePassword
    {
        $this->context->update([
            'dms_passwords_and_keys' => [
                'title' => 'DMS keys',
                'key_names' => [
                    'redis_password' => ['type' => 'password', 'include' => ['letters', 'numbers'], 'length' => 14],
                    'bucket_user_password' => ['type' => 'password', 'include' => ['letters', 'numbers'], 'length' => 14],
                    'app_key' => ['type' => 'laravel_app_key'],
                    'pusher_app_key' => ['type' => 'random_bin2hex', 'length' => 16],
                    'pusher_app_secret' => ['type' => 'random_bin2hex', 'length' => 16],
                    'pusher_app_id' => ['type' => 'random_bin2hex', 'length' => 16],
                    'mysql_root_password' => ['type' => 'password', 'include' => ['letters', 'numbers'], 'length' => 14],
                    'mysql_password' => ['type' => 'password', 'include' => ['letters', 'numbers'], 'length' => 14],
                    'elasticsearch_password' => ['type' => 'password', 'include' => ['letters', 'numbers'], 'length' => 14],
                ],
            ],
        ]);

        $dmsPasswordsAndKeys = new GeneratePassword();
        $dmsPasswordsAndKeys
            ->provideName('Generate Redis/Pusher/Minio/etc passwords and keys for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('dms_passwords_and_keys'));

        return $dmsPasswordsAndKeys;
    }

    private function getCreateBucketStage(string $name): CreateBucket
    {
        $this->context->update([
            'create_bucket' => [
                'username' => sprintf('%s', $name),
                'bucketname' => sprintf('%s', $name),
                'policyname' => sprintf('%s', $name),
            ],
        ]);

        $createBucket = new CreateBucket();
        $createBucket
            ->provideName('Create Minio bucket for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('create_bucket', [
                'userpassword' => 'dms_passwords_and_keys.bucket_user_password',
            ]));

        return $createBucket;
    }

    private function getDockerComposeDeployStage(
        string|null $sentryDsn,
        string $serverName,
        array|null $awsData,
        string $hostname,
        string $subdomain,
        string $url,
    ): DockerComposeDeploy {
        $this->context->update([
            'dms_docker_compose' => [
                'env_template_generator' => DmsEnvFromContextGenerator::class,
                'yaml_template_generator' => DmsYamlFromContextGenerator::class,
                'user' => config('deploy.dms.ssh_user'),
                'private_key' => config('services.hetzner.cloud.ssh_key_file'),
                'registry_user' => config('deploy.dms.registry_user'),
                'registry_password' => config('deploy.dms.registry_password'),
                'docker_networks' => ['dms_shared_network'],
                'prefix_compose_files' => false,

                // ENV template generator values.
                'vm_name' => $serverName,
                'hostname' => $hostname,
                'subdomain' => $subdomain,
                'domain' => config('deploy.dms.zone_name'),
                'pwa_subdomain' => (new PwaData())->getSubdomain($subdomain),
                'pwa_domain' => config('deploy.pwa.zone_name'),
                'registry' => config('deploy.dms.registry'),
                'tag' => $this->targetVersion ? $this->targetVersion->tag : config('deploy.dms.tag'),
                'register_admin_email' => $this->payload->data['email'],
                'company_name' => $this->payload->data['company_name'],
                'with_pwa' => $this->payload->data['with_pwa'] ?? false,
                'url' => $url,
                'environment' => config('deploy.environment'),
                'zbx_metadata' => config('deploy.zbx.metadata'),
                'elasticsearch_host' => config('deploy.dms.elasticsearch.host'),
                'pdf_base_url' => config('deploy.dms.pdf_base_url'),
                'compose_project_name' => config('deploy.dms.compose_project_name'),
                'google_maps_api_key' => config('deploy.dms.google_maps.api_key'),
                'mail_mailer' => config('deploy.dms.mail.mailer'),
                'mail_host' => config('deploy.dms.mail.host'),
                'mail_port' => config('deploy.dms.mail.port'),
                'mail_username' => config('deploy.dms.mail.username'),
                'mail_password' => config('deploy.dms.mail.password'),
                'mail_encryption' => config('deploy.dms.mail.encryption'),
                'mail_from_address' => config('deploy.dms.mail.from_address'),

                'marketplaces_encryption_key' => config('deploy.dms.marketplaces_encryption_key'),

                'sentry_release' => config('deploy.dms.sentry.release'),
                'sentry_environment' => config('deploy.dms.sentry.environment'),
            ],
        ]);

        if ($this->targetVersion) {
            $this->context->set('dms_docker_compose.version_id', $this->targetVersion->id);
        }

        $refs = [
            'host' => 'create_hetzner_vm.server_public_ip',
            'instance_id' => 'create_hetzner_vm.instance_id',
            'checker_base_url' => 'checker_for_dms.base_url',
            'checker_client_id' => 'checker_for_dms.client_id',
            'checker_client_secret' => 'checker_for_dms.client_secret',
            'ai_service_base_url' => 'ai_service_for_dms.base_url',
            'ai_service_api_token' => 'ai_service_for_dms.api_token',
            'scrm_base_url' => 'scrm_for_dms.base_url',
            'scrm_client_id' => 'scrm_for_dms.client_id',
            'scrm_client_secret' => 'scrm_for_dms.client_secret',
            'scrm_grant_type' => 'scrm_for_dms.grant_type',
            'scrm_scope' => 'scrm_for_dms.scope',
            'envelope_base_url' => 'envelope_for_dms.base_url',
            'envelope_app_id' => 'envelope_for_dms.app_id',
            'envelope_auth_key_id' => 'envelope_for_dms.auth_key_id',
            'envelope_auth_key_secret' => 'envelope_for_dms.auth_key_secret',
            'redis_password' => 'dms_passwords_and_keys.redis_password',
            'app_key' => 'dms_passwords_and_keys.app_key',
            'pusher_app_id' => 'dms_passwords_and_keys.pusher_app_id',
            'pusher_app_key' => 'dms_passwords_and_keys.pusher_app_key',
            'pusher_app_secret' => 'dms_passwords_and_keys.pusher_app_secret',
            'mysql_password' => 'dms_passwords_and_keys.mysql_password',
            'mysql_root_password' => 'dms_passwords_and_keys.mysql_root_password',
        ];

        // If we have predetermined Sentry DSN for this project then we use it.
        if (! empty($sentryDsn)) {
            $this->context->set('dms_docker_compose.sentry_dsn', $sentryDsn);
        } else {
            // Otherwise we reference the one written in 'sentry_project_for_dms'.
            $refs['sentry_dsn'] = 'sentry_project_for_dms.project_dsn';
        }

        // If we have predetermined AWS data for this project then we use it.
        if (! empty($awsData)) {
            $this->context->set('dms_docker_compose.aws_url', $awsData['url']);
            $this->context->set('dms_docker_compose.aws_bucket', $awsData['bucket']);
            $this->context->set('dms_docker_compose.aws_access_key_id', $awsData['access_key_id']);
            $this->context->set('dms_docker_compose.aws_access_secret_key', $awsData['access_secret_key']);
        } else {
            // Otherwise we reference the one written in 'create_bucket'.
            $this->context->set('dms_docker_compose.aws_url', config('deploy.cdn.url'));
            $refs['aws_bucket'] = 'create_bucket.bucket_name';
            $refs['aws_access_key_id'] = 'create_bucket.bucket_access_key';
            $refs['aws_access_secret_key'] = 'create_bucket.bucket_secret_key';
        }

        if (config('deploy.dms.elasticsearch.enable_auth')) {
            $this->context->set('dms_docker_compose.elasticsearch_user', config('deploy.dms.elasticsearch.user'));
            if (config('deploy.dms.elasticsearch.generate_password')) {
                $refs['elasticsearch_password'] = 'dms_passwords_and_keys.elasticsearch_password';
            } else {
                $this->context->set('dms_docker_compose.elasticsearch_password', config('deploy.dms.elasticsearch.password'));
            }
        }

        $dockerComposeDeploy = new DockerComposeDeploy();
        $dockerComposeDeploy
            ->provideName('Deploy docker compose on server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('dms_docker_compose', $refs));

        return $dockerComposeDeploy;
    }

    private function getWaitForDmsToDeployStage(): WaitForDmsToDeploy
    {
        $this->context->update([
            'wait_for_dms_to_deploy' => [
                'email' => $this->payload->data['email'],
                'company_name' => $this->payload->data['company_name'],
            ],
        ]);

        $waitForDmsToDeploy = new WaitForDmsToDeploy();
        $waitForDmsToDeploy
            ->provideName('Waiting for DMS to finish deployment')
            ->provideContext($this->context)
            ->provideScope(new StageScope('wait_for_dms_to_deploy', [
                'host' => 'create_hetzner_vm.server_public_ip',
                'user' => 'dms_docker_compose.user',
                'private_key' => 'dms_docker_compose.private_key',
                'with_pwa' => 'dms_docker_compose.with_pwa',
            ]));

        return $waitForDmsToDeploy;
    }

    private function getCreateDmsInstanceStage(
        string $hostname,
        string $subdomain,
        string $serverName,
        string $url,
        string $email,
    ): CreateDmsInstance {
        $this->context->update([
            'create_dms_instance' => [
                'name' => $this->payload->data['company_name'],
                'server_name' => $serverName,
                'subdomain' => $subdomain,
                'email' => $email,
                'hostname' => $hostname,
                'url' => $url,
                'new_group_name' => sprintf(
                    'basic-dms-setup-%s',
                    $subdomain,
                ),
            ],
        ]);

        $createDmsInstance = new CreateDmsInstance();
        $createDmsInstance
            ->provideName('Save DMS instance record in database')
            ->provideContext($this->context)
            ->provideScope(new StageScope('create_dms_instance', [
                'ssh_user' => 'dms_docker_compose.user',
                'private_key' => 'dms_docker_compose.private_key',
                'server_public_ip' => 'create_hetzner_vm.server_public_ip',
                'server_external_id' => 'create_hetzner_vm.server_id',
                'server_external_name' => 'create_hetzner_vm.server_name',
                'oauth_hub_client_id' => 'wait_for_dms_to_deploy.result_hub_client_id',
                'oauth_hub_client_secret' => 'wait_for_dms_to_deploy.result_hub_client_secret',
                'oauth_pwa_client_id' => 'wait_for_dms_to_deploy.result_pwa_client_id',
                'oauth_pwa_client_secret' => 'wait_for_dms_to_deploy.result_pwa_client_secret',
                'pusher_app_id' => 'dms_passwords_and_keys.pusher_app_id',
                'pusher_app_key' => 'dms_passwords_and_keys.pusher_app_key',
                'pusher_app_secret' => 'dms_passwords_and_keys.pusher_app_secret',
                'sentry_project_data' => 'sentry_project_for_dms.project_data',
                'sentry_project_client_key_data' => 'sentry_project_for_dms.project_client_key_data',
            ]));

        return $createDmsInstance;
    }

    private function getSendDmsInviteStage(): SendDmsInvite
    {
        $this->context->update([
            'send_dms_invite' => [
                //
            ],
        ]);

        $createDmsInstance = new SendDmsInvite();
        $createDmsInstance
            ->provideName('Sending DMS invite for root user')
            ->provideContext($this->context)
            ->provideScope(new StageScope('send_dms_invite', [
                'host' => 'create_hetzner_vm.server_public_ip',
                'user' => 'dms_docker_compose.user',
                'private_key' => 'dms_docker_compose.private_key',
            ]));

        return $createDmsInstance;
    }

	private function getRegisterSubscriptionStage(): RegisterSubscription
	{
		$this->context->update([
			'subscription_for_dms' => [
				'subscription' => $this->payload->data['extra']['subscription'],
				'system_features_stats' => $this->payload->data['extra']['systemFeaturesStats'],
				'service_features' => $this->payload->data['extra']['serviceFeatures'],
				'mode' => 'create',
			],
		]);

		$registerSubscription = new RegisterSubscription();
		$registerSubscription
			->provideName('Register subscription for DMS...')
			->provideContext($this->context)
			->provideScope(new StageScope('subscription_for_dms', [
				'host' => 'create_hetzner_vm.server_public_ip',
				'user' => 'dms_docker_compose.user',
				'private_key' => 'dms_docker_compose.private_key',
			]));

		return $registerSubscription;
	}
}
