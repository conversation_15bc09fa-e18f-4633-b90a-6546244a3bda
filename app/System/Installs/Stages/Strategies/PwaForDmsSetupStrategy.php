<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Data\DmsInstanceData;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Generators\Docker\PwaEnvFromContextGenerator;
use App\System\Installs\Stages\Generators\Docker\PwaYamlFromContextGenerator;
use App\System\Installs\Stages\Implementations\Common\CheckUrlStatus;
use App\System\Installs\Stages\Implementations\Common\GeneratePassword;
use App\System\Installs\Stages\Implementations\Dms\RearrangeDmsForPwa;
use App\System\Installs\Stages\Implementations\Dms\SendDmsInvite;
use App\System\Installs\Stages\Implementations\Docker\DockerComposeDeploy;
use App\System\Installs\Stages\Implementations\Hetzner\RegisterDns;
use App\System\Installs\Stages\Implementations\Pwa\ActivatePwaInDms;
use App\System\Installs\Stages\Implementations\Pwa\CreatePwa;
use App\System\Installs\Stages\Implementations\Pwa\FinishPwaInstallation;
use App\System\Installs\Stages\Implementations\Pwa\Support\PwaData;
use App\System\Installs\Stages\Implementations\Sentry\CreateSentryProject;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class PwaForDmsSetupStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;
    private SshConnection|null $sshConnection = null;
    private Version|null $targetVersion = null;

    public function __construct(private InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'dms_id' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::DMS)],
        ]);
        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['dms_id']);
        }

        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;
        $dataToValidate = [
            'name' => $dmsData->name,
            'subdomain' => $dmsData->subdomain,
            'hostname' => $dmsData->hostname,
            'url' => $dmsData->url,
        ];

        $dataValidator = Validator::make($dataToValidate, [
            'name' => ['required'],
            'subdomain' => ['required'],
            'hostname' => ['required'],
            'url' => ['required'],
        ]);

        if ($dataValidator->fails()) {
            throw ValidationException::withMessages([
                'dms_id' => 'Given DMS ID is not compatible with PWA setup strategy!',
            ]);
        }

        if (! $this->wasInstalledWithoutPwa()) {
            if (! isset($dmsData->oauthClients['pwa'])) {
                throw ValidationException::withMessages([
                    'dms_id' => 'Given DMS ID is missing required ouath credentials for PWA!',
                ]);
            }
        }

        $this->sshConnection = $this->instance->sshConnections->first();
        if (empty($this->sshConnection)) {
            throw ValidationException::withMessages([
                'dms_id' => 'Given DMS ID is missing SSH connection required for PWA setup!',
            ]);
        }

        if (! empty($this->payload->data['target_version_id'])) {
            if (! $this->targetVersion) {
                $this->targetVersion = Version::where('id', '=', $this->payload->data['target_version_id'])
                     ->where('app_type', '=', AppType::PWA->value)
                     ->firstOrFail();
            }
        }

        /**
         * NOTE
         * We do not check if this DMS instance already has connected PWA instance. So far multiple PWA instances
         * can only depend on a single DMS one.
         */
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $hostname = (new PwaData())->getHostname($dmsData->subdomain);
        $url = sprintf('https://%s', $hostname);
        $sentryDsn = config('deploy.pwa.sentry.dsn');

        $this->context = new Context();

        $stages = collect();

        $dmsOauthRefs = null;
        $wasInstalledWithoutPwa = $this->wasInstalledWithoutPwa();
        if ($wasInstalledWithoutPwa) {
            // If DMS was installed without PWA then we add additional stage
            // that will create OAuth credentials for PWA...
            $stages->push($this->getRearrangeDmsForPwaStage());

            // ... and reference those values for following CreatePwaStage.
            $dmsOauthRefs = [
                'dms_client_id' => 'rearrange_dms_for_pwa.result_pwa_client_id',
                'dms_client_secret' => 'rearrange_dms_for_pwa.result_pwa_client_secret',
            ];

            // Otherwise $dmsOauthRefs will be null and CreatePwaStage will get OAuth credentials from existing
            // DMS instance data.
        }

        $stages->push($this->getCreatePwaStage($dmsOauthRefs));
        $stages->push($this->getRegisterDnsStage());
        $stages->push($this->getPwaPasswordsAndKeysStage());

        // If we don't have predetermined Sentry DSN then we include Sentry project creation stage.
        if (empty($sentryDsn)) {
            $stages->push($this->getCreateSentryProjectStage());
        }

        $stages->push($this->getDockerComposeDeployStage($dmsOauthRefs, $sentryDsn, $hostname, $url));
        $stages->push($this->getActivatePwaInDmsStage());
        $stages->push($this->getCheckDmsAvailabilityStage());
        $stages->push($this->getCheckPwaAvailabilityStage($url));
        $stages->push($this->getFinishPwaInstallationStage());

        // Only send invite if DMS was not already installed without PWA in the past and this is new DMS instance.
        if (! $wasInstalledWithoutPwa) {
            $stages->push($this->getSendDmsInviteStage());
        }

        return new Stages($stages->all(), $this->context);
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        return [
            'pwa' => [
                'id' => $context->get('pwa.instance_id'),
                'url' => $context->get('pwa.url'),
                'name' => $context->get('pwa.name'),

                // Additional SCRM compatible fields.
                'release_id' => $this->targetVersion ? $this->targetVersion->id : null,
                'instance_release_id' => $this->install ? $this->install->id : null,
            ],
        ];
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        $groupId = $context->get('pwa.group_id');

        return Group::withTrashed()->firstWhere('id', '=', $groupId);
    }

    private function getRearrangeDmsForPwaStage(): RearrangeDmsForPwa
    {
        $this->context->update([
            'rearrange_dms_for_pwa' => [
                'dms_id' => $this->payload->data['dms_id'],
            ],
        ]);

        $rearrangeDmsForPwa = new RearrangeDmsForPwa();
        $rearrangeDmsForPwa
            ->provideName('Prepare existing DMS instance for new PWA one')
            ->provideContext($this->context)
            ->provideScope(new StageScope('rearrange_dms_for_pwa'));

        return $rearrangeDmsForPwa;
    }

    private function getCreatePwaStage(array|null $dmsOauthRefs = null): CreatePwa
    {
        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'pwa' => [
                'api_client_id' => config('deploy.pwa.client_id'),
                'api_client_secret' => config('deploy.pwa.client_secret'),
                'ssh_connection_id' => $this->sshConnection->id,
                'group_id' => $this->instance->group_id,
                'name' => $dmsData->name,
                'subdomain' => $dmsData->subdomain,
                'dms_url' => $dmsData->url,
                'dms_pusher_app_key' => $dmsData->pusherData->appKey,
                'dms_pusher_app_secret' => $dmsData->pusherData->appSecret,
            ],
        ]);

        $refs = [];

        if (empty($dmsOauthRefs)) {
            $dmsOauthForPwa = $dmsData->getOauthClientById('pwa');

            $this->context->set('pwa.dms_client_id', $dmsOauthForPwa->clientId);
            $this->context->set('pwa.dms_client_secret', $dmsOauthForPwa->clientSecret);
        } else {
            $refs['dms_client_id'] = $dmsOauthRefs['dms_client_id'];
            $refs['dms_client_secret'] = $dmsOauthRefs['dms_client_secret'];
        }

        $createPwa = new CreatePwa();
        $createPwa
            ->provideName('Create PWA in admin part')
            ->provideContext($this->context)
            ->provideScope(new StageScope('pwa', $refs));

        return $createPwa;
    }

    private function getRegisterDnsStage(): RegisterDns
    {
        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'dns' => [
                'zone_name' => config('deploy.pwa.zone_name'),
                'ip' => $dmsData->ip,
                'subdomain' => (new PwaData())->getSubdomain($dmsData->subdomain),
            ],
        ]);

        $registerDns = new RegisterDns();
        $registerDns
            ->provideName('Register public DNS zone and record for server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('dns'));

        return $registerDns;
    }

    private function getPwaPasswordsAndKeysStage(): GeneratePassword
    {
        $this->context->update([
            'pwa_passwords_and_keys' => [
                'title' => 'PWA keys',
                'key_names' => [
                    'app_key' => ['type' => 'laravel_app_key'],
                ],
            ],
        ]);

        $pwaPasswordsAndKeys = new GeneratePassword();
        $pwaPasswordsAndKeys
            ->provideName('Generate passwords and keys for PWA...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('pwa_passwords_and_keys'));

        return $pwaPasswordsAndKeys;
    }

    private function getCreateSentryProjectStage(): CreateSentryProject
    {
        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'sentry_project_for_pwa' => [
                'organization_slug' => config('deploy.sentry.organization_slug'),
                'team_slug' => config('deploy.sentry.team_slug'),
                'project_name' => sprintf(
                    config('deploy.sentry.pwa_project_name_template'),
                    $dmsData->subdomain,
                ),
                'platform' => 'php-laravel',
            ],
        ]);

        $createSentryProject = new CreateSentryProject();
        $createSentryProject
            ->provideName('Create Sentry project for PWA...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('sentry_project_for_pwa', [
                //
            ]));

        return $createSentryProject;
    }

    private function getDockerComposeDeployStage(
        array|null $dmsOauthRefs = null,
        string|null $sentryDsn,
        string $hostname,
        string $url
    ): DockerComposeDeploy {
        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'pwa_docker_compose' => [
                'env_template_generator' => PwaEnvFromContextGenerator::class,
                'yaml_template_generator' => PwaYamlFromContextGenerator::class,
                'host' => $this->sshConnection->host,
                'user' => $this->sshConnection->user,
                'private_key' => $this->sshConnection->private_key,
                'registry_user' => config('deploy.pwa.registry_user'),
                'registry_password' => config('deploy.pwa.registry_password'),

                // ENV template generator values.
                'vm_name' => $dmsData->externalName,
                'hostname' => $hostname,
                'url' => $url,
                'environment' => config('deploy.environment'),
                'registry' => config('deploy.pwa.registry'),
                'tag' => $this->targetVersion ? $this->targetVersion->tag : config('deploy.pwa.tag'),
                'name' => $dmsData->name,
                'compose_project_name' => config('deploy.pwa.compose_project_name'),
                'zone_name' => config('deploy.pwa.zone_name'),
                'app_admin_url' => config('deploy.pwa.admin_url'),
                'app_admin_client_id' => config('deploy.pwa.admin_client_id'),
                'app_admin_secret' => config('deploy.pwa.admin_secret'),
                'mail_mailer' => config('deploy.pwa.mail.mailer'),
                'mail_host' => config('deploy.pwa.mail.host'),
                'mail_port' => config('deploy.pwa.mail.port'),
                'mail_username' => config('deploy.pwa.mail.username'),
                'mail_password' => config('deploy.pwa.mail.password'),
                'mail_encryption' => config('deploy.pwa.mail.encryption'),
                'mail_from_address' => config('deploy.pwa.mail.from_address'),
                'vapid_public_key' => config('deploy.pwa.vapid_public_key'),
                'vapid_private_key' => config('deploy.pwa.vapid_private_key'),

                'sentry_release' => config('deploy.pwa.sentry.release'),
                'sentry_environment' => config('deploy.pwa.sentry.environment'),
            ],
        ]);

        if ($this->targetVersion) {
            $this->context->set('pwa_docker_compose.version_id', $this->targetVersion->id);
        }

        $refs = [
            'instance_id' => 'dms.instance_id',

            'app_key' => 'pwa_passwords_and_keys.app_key',
            'app_id' => 'pwa.client_id',
            'app_secret_key' => 'pwa.secret_key',
            'dms_url' => 'pwa.dms_url',
            'dms_pusher_app_key' => 'pwa.dms_pusher_app_key',
            'dms_pusher_app_secret' => 'pwa.dms_pusher_app_secret',
            'mail_from_name' => 'pwa.name',
        ];

        // If no specific DMS Oauth refs are provided then we are taking them from CreatePwa ('pwa') stage context.
        if (empty($dmsOauthRefs)) {
            $refs['dms_client_id'] = 'pwa.dms_client_id';
            $refs['dms_client_secret'] = 'pwa.dms_client_secret';
        } else {
            $refs['dms_client_id'] = $dmsOauthRefs['dms_client_id'];
            $refs['dms_client_secret'] = $dmsOauthRefs['dms_client_secret'];
        }

        // If we have predetermined Sentry DSN for this project then we use it.
        if (! empty($sentryDsn)) {
            $this->context->set('pwa_docker_compose.sentry_dsn', $sentryDsn);
        } else {
            // Otherwise we reference the one written in 'sentry_project_for_pwa'.
            $refs['sentry_dsn'] = 'sentry_project_for_pwa.project_dsn';
        }

        $dockerComposeDeploy = new DockerComposeDeploy();
        $dockerComposeDeploy
            ->provideName('Deploy docker compose on server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('pwa_docker_compose', $refs));

        return $dockerComposeDeploy;
    }

    private function getActivatePwaInDmsStage(): ActivatePwaInDms
    {
        $this->context->update([
            'activate_pwa_in_dms' => [
                'dms_id' => $this->payload->data['dms_id'],
            ],
        ]);

        $activatePwaInDms = new ActivatePwaInDms();
        $activatePwaInDms
            ->provideName('Activate PWA in existing DMS instance')
            ->provideContext($this->context)
            ->provideScope(new StageScope('activate_pwa_in_dms', [
                'base_url' => 'pwa.url',
                'pwa_client_id' => 'pwa.client_id',
                'pwa_client_secret' => 'pwa.secret_key',
            ]));

        return $activatePwaInDms;
    }

    private function getCheckDmsAvailabilityStage(): CheckUrlStatus
    {
        /** @var DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'check_dms_availability' => [
                'url' => $dmsData->url,
                'number_of_tries' => 28, // 28 tries every 30 seconds = 840 seconds.
                'try_every' => 30,
                'check_ssl' => true,
            ],
        ]);

        $checkDmsAvailability = new CheckUrlStatus();
        $checkDmsAvailability
            ->provideName('Waiting for DMS URL to be accessible')
            ->provideContext($this->context)
            ->provideScope(new StageScope('check_dms_availability'));

        return $checkDmsAvailability;
    }

    private function getCheckPwaAvailabilityStage(string $url): CheckUrlStatus
    {
        $this->context->update([
            'check_pwa_availability' => [
                'url' => $url,
                'number_of_tries' => 28, // 28 tries every 30 seconds = 840 seconds.
                'try_every' => 30,
                'check_ssl' => true,
            ],
        ]);

        $checkPwaAvailability = new CheckUrlStatus();
        $checkPwaAvailability
            ->provideName('Waiting for PWA URL to be accessible')
            ->provideContext($this->context)
            ->provideScope(new StageScope('check_pwa_availability'));

        return $checkPwaAvailability;
    }

    private function getFinishPwaInstallationStage(): FinishPwaInstallation
    {
        $this->context->update([
            'finish_pwa_installation' => [
                //
            ],
        ]);

        $finishPwaInstallation = new FinishPwaInstallation();
        $finishPwaInstallation
            ->provideName('Running additional commands on PWA to finish its installation')
            ->provideContext($this->context)
            ->provideScope(new StageScope('finish_pwa_installation', [
                'host' => 'pwa_docker_compose.host',
                'user' => 'pwa_docker_compose.user',
                'private_key' => 'pwa_docker_compose.private_key',
            ]));

        return $finishPwaInstallation;
    }

    private function getSendDmsInviteStage(): SendDmsInvite
    {
        $this->context->update([
            'send_dms_invite' => [
                //
            ],
        ]);

        $sendDmsInvite = new SendDmsInvite();
        $sendDmsInvite
            ->provideName('Sending DMS invite')
            ->provideContext($this->context)
            ->provideScope(new StageScope('send_dms_invite', [
                'host' => 'pwa_docker_compose.host',
                'user' => 'pwa_docker_compose.user',
                'private_key' => 'pwa_docker_compose.private_key',
            ]));

        return $sendDmsInvite;
    }

    private function wasInstalledWithoutPwa(): bool
    {
        $dmsInstall = $this->instance->group->installs()
                                                ->where('type', '=', InstallType::BASIC_DMS_SETUP->value)
                                                ->currentStatus(InstallStatus::FINISHED)
                                                ->first();

        return ! $dmsInstall->payload['with_pwa'];
    }
}
