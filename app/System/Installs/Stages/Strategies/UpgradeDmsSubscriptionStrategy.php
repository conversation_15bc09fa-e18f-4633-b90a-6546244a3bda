<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Docker\RegisterSubscription;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class UpgradeDmsSubscriptionStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;
    private SshConnection|null $sshConnection = null;

    public function __construct(private readonly InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'id' => ['required', 'string'],

            'deployerDmsId' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::DMS->value)],

            'systemFeaturesStats' => ['required', 'array'],
            'systemFeaturesStats.*' => ['nullable', 'array'],
            'systemFeaturesStats.*.id' => ['required', 'string'],
            'systemFeaturesStats.*.type' => ['required', 'string'],
            'systemFeaturesStats.*.title' => ['nullable', 'string'],
            'systemFeaturesStats.*.total' => ['required', 'numeric'],
            'systemFeaturesStats.*.current' => ['required', 'numeric'],
            'systemFeaturesStats.*.units' => ['required', 'string'],

            'serviceFeatures' => ['required', 'array'],
            'serviceFeatures.*' => ['nullable', 'array'],
            'serviceFeatures.*.id' => ['required', 'string'],
            'serviceFeatures.*.title' => ['nullable', 'string'],
            'serviceFeatures.*.type' => ['required', 'string'],

            'subscription' => ['nullable', 'array'],
            'subscription.id' => ['required', 'string'],
            'subscription.status' => ['required', 'string'],
            'subscription.nextPeriodStartAt' => ['nullable'],
            'subscription.lastPeriodEndsAt' => ['nullable'],
            'subscription.nextPaymentAttempsAt' => ['nullable'],
            'subscription.expiresAt' => ['nullable'],
            'subscription.gracePeriodExpiresAt' => ['nullable'],
            'subscription.startsAt' => ['nullable'],
            'subscription.endsAt' => ['nullable'],
            'subscription.trialEndsAt' => ['nullable'],
            'subscription.cancelledAt' => ['nullable'],
            'subscription.cancelAtPeriodEnd' => ['required', 'boolean'],
            'subscription.billingCycle' => ['required', 'string'],

            'subscription.plan' => ['nullable', 'array'],
            'subscription.plan.id' => ['required', 'string'],
            'subscription.plan.number' => ['required', 'numeric'],
            'subscription.plan.name' => ['required', 'string'],
            'subscription.plan.status' => ['required', 'string'],
            'subscription.plan.type' => ['required', 'string'],
            'subscription.plan.price' => ['required', 'numeric'],
            'subscription.plan.yearly_price' => ['required', 'numeric'],
            'subscription.plan.trialPeriodDays' => ['nullable', 'numeric'],
            'subscription.plan.isActive' => ['required', 'boolean'],
            'subscription.plan.hasTrial' => ['required', 'boolean'],
            'subscription.plan.startDate' => ['nullable', 'date'],
            'subscription.plan.endDate' => ['nullable', 'date'],

            'subscription.plan.currency' => ['nullable', 'array'],
            'subscription.plan.currency.id' => ['required', 'string'],
            'subscription.plan.currency.code' => ['required', 'string'],
            'subscription.plan.currency.symbol' => ['required', 'string'],
            'subscription.plan.currency.name' => ['required', 'string'],
            'subscription.plan.currency.isActive' => ['required', 'boolean'],
        ]);

        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['deployerDmsId']);
        }

        $this->sshConnection = $this->instance->sshConnections->first();
        if (empty($this->sshConnection)) {
            throw ValidationException::withMessages([
                'dms_id' => 'Given DMS ID is missing SSH connection required for PWA setup!',
            ]);
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $this->context = new Context();

        $stages = collect();

        $stages->push($this->getUpdateDmsSubscription());

        return new Stages($stages->all(), $this->context);
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        return null;
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        return [
            //
        ];
    }

    private function getUpdateDmsSubscription(): RegisterSubscription
    {
        $this->context->update([
            'subscription_for_dms' => [
                'subscription' => $this->payload->data['subscription'],
                'system_features_stats' => $this->payload->data['systemFeaturesStats'],
                'service_features' => $this->payload->data['serviceFeatures'],
                'host' => $this->sshConnection->host,
                'user' => $this->sshConnection->user,
                'private_key' => $this->sshConnection->private_key,
                'mode' => 'update',
            ],
        ]);

        $registerSubscription = new RegisterSubscription();
        $registerSubscription
            ->provideName('Update subscription for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('subscription_for_dms'));

        return $registerSubscription;
    }
}
