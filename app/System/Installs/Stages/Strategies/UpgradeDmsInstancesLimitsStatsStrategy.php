<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Connections\SshConnection;
use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Docker\UpdateDmsLimitsStats;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class UpgradeDmsInstancesLimitsStatsStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;
    private SshConnection|null $sshConnection = null;

    public function __construct(private readonly InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'id' => ['required', 'string'],
            'deployerDmsId' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::DMS->value)],
        ]);

        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['deployerDmsId']);
        }

        $this->sshConnection = $this->instance->sshConnections->first();
        if (empty($this->sshConnection)) {
            throw ValidationException::withMessages([
                'dms_id' => 'Given DMS ID is missing SSH connection required for PWA setup!',
            ]);
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $this->context = new Context();

        $stages = collect();

        $stages->push($this->getUpdateDmsLimitsStats());

        return new Stages($stages->all(), $this->context);
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        return null;
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        return [
            //
        ];
    }

    private function getUpdateDmsLimitsStats(): UpdateDmsLimitsStats
    {
        $this->context->update([
            'limits_stats_for_dms' => [
                'host' => $this->sshConnection->host,
                'user' => $this->sshConnection->user,
                'private_key' => $this->sshConnection->private_key,
            ],
        ]);

        $updateDmsLimitsStats = new UpdateDmsLimitsStats();
        $updateDmsLimitsStats
            ->provideName('Update limits stats for DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('limits_stats_for_dms'));

        return $updateDmsLimitsStats;
    }
}
