<?php

namespace App\System\Installs\Stages\Strategies;

use App\Models\Infrastructure\Group;
use App\Models\Infrastructure\Instance;
use App\Models\Installs\Install;
use App\System\Apps\Data\AppType;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallStatus;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StagesStrategy;
use App\System\Installs\Stages\Data\Stages;
use App\System\Installs\Stages\Data\StageScope;
use App\System\Installs\Stages\Implementations\Dms\DeleteDmsInstanceAndGroup;
use App\System\Installs\Stages\Implementations\Dms\DeleteDmsResources;
use App\System\Installs\Stages\Implementations\Docker\EnvelopeSupport\DeleteEnvelopeApp;
use App\System\Installs\Stages\Implementations\Hetzner\DeleteDns;
use App\System\Installs\Stages\Implementations\Hetzner\DeleteServer;
use App\System\Installs\Stages\Implementations\Pwa\DeletePwa;
use App\System\Installs\Traits\MayHaveInstall;
use App\System\Runtime\Context;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use LogicException;

class DeleteDmsInstanceStrategy implements StagesStrategy
{
    use MayHaveInstall;

    private Context $context;
    private Instance|null $instance = null;

    public function __construct(private InstallPayload $payload)
    {
        //
    }

    public function assertItIsValid(): void
    {
        $validator = Validator::make($this->payload->data, [
            'dms_id' => ['required', Rule::exists('instances', 'id')->where('app_type', AppType::DMS)],
        ]);
        $validator->validate();

        if (! $this->instance) {
            $this->instance = Instance::findOrFail($this->payload->data['dms_id']);
        }
    }

    public function getStages(): Stages
    {
        $this->assertItIsValid();

        $this->context = new Context();

        $dmsInstallContext = $this->getInstallContext(InstallType::BASIC_DMS_SETUP);

        $stages = collect();

        $stages->push($this->getDeleteDmsResourcesStage(
            $dmsInstallContext->get('create_bucket'),
            $dmsInstallContext->get('sentry_project_for_dms'),
        ));
        $stages->push($this->getDeleteDnsStage($dmsInstallContext->get('dns')));

        $envelopeForDms = $dmsInstallContext->get('envelope_for_dms');
        if(!empty($envelopeForDms)) {
            $stages->push($this->getDeleteEnvelopeApp($envelopeForDms));
        }

        $pwaInstance = $this->checkForPwaInstance();
        if ($pwaInstance) {
            $pwaInstallContext = $this->getInstallContext(InstallType::PWA_FOR_DMS_SETUP);

            $stages->push($this->getDeletePwaStage($pwaInstance, $pwaInstallContext->get('pwa')));
        }

        $stages->push($this->getDeleteServerStage());
        $stages->push($this->getDeleteDmsInstanceAndGroupStage());

        return new Stages($stages->all(), $this->context);
    }

    public function getResult(Context|null $context): array|null
    {
        $this->assertItIsValid();

        if (! $context) {
            return null;
        }

        return [
            //
        ];
    }

    public function getGroup(Context|null $context): Group|null
    {
        $this->assertItIsValid();

        return $this->instance->group;
    }

    private function checkForPwaInstance(): Instance|null
    {
        $pwaInstances = $this->instance->group->instances()->where('app_type', '=', AppType::PWA->value)->get();

        if ($pwaInstances->count() > 1) {
            throw new LogicException(sprintf(
                'Multiple PWA instances found for DMS instance ID %d!',
                $this->instance->id,
            ));
        }

        if ($pwaInstances->count() === 0) {
            return null;
        }

        return $pwaInstances->first();
    }

    private function getDeleteDmsResourcesStage(array $bucketData, array $sentryData): DeleteDmsResources
    {
        $this->context->update([
            'delete_dms_resources' => [
                'bucket' => $bucketData,
                'sentry' => $sentryData,
            ],
        ]);

        $deleteDmsResources = new DeleteDmsResources();
        $deleteDmsResources
            ->provideName('Delete DMS instance related resources')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_dms_resources'));

        return $deleteDmsResources;
    }

    private function getDeleteDnsStage(array $dns): DeleteDns
    {
        $this->context->update([
            'delete_dns_record' => [
                'dns' => $dns,
            ],
        ]);

        $deleteDns = new DeleteDns();
        $deleteDns
            ->provideName('Delete DNS record')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_dns_record'));

        return $deleteDns;
    }

    private function getDeleteEnvelopeApp(array $envelopeForDms): DeleteEnvelopeApp
    {
        $this->context->update([
            'delete_envelope_for_dms' => [
                'envelope_for_dms' => $envelopeForDms,
            ],
        ]);

        $deleteDns = new DeleteEnvelopeApp();
        $deleteDns
            ->provideName('Delete Envelope app of DMS...')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_envelope_for_dms'));

        return $deleteDns;
    }

    private function getDeletePwaStage(Instance $pwaInstance, array $pwa): DeletePwa
    {
        $this->context->update([
            'delete_pwa' => [
                'pwa_id' => $pwaInstance->id,
                'data' => $pwa,
            ],
        ]);

        $deletePwa = new DeletePwa();
        $deletePwa
            ->provideName('Delete PWA record')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_pwa'));

        return $deletePwa;
    }

    private function getDeleteServerStage(): DeleteServer
    {
        /** @var \App\System\Infrastructure\Instances\Data\DmsInstanceData */
        $dmsData = $this->instance->data;

        $this->context->update([
            'delete_server' => [
                'server_id' => $dmsData->externalId,
            ],
        ]);

        $deleteServer = new DeleteServer();
        $deleteServer
            ->provideName('Delete server')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_server'));

        return $deleteServer;
    }

    private function getDeleteDmsInstanceAndGroupStage(): DeleteDmsInstanceAndGroup
    {
        $this->context->update([
            'delete_dms_instance_and_group' => [
                'group_id' => $this->instance->group->id,
                'instance_id' => $this->instance->id,
            ],
        ]);

        $deleteServer = new DeleteDmsInstanceAndGroup();
        $deleteServer
            ->provideName('Delete DMS and group database records')
            ->provideContext($this->context)
            ->provideScope(new StageScope('delete_dms_instance_and_group'));

        return $deleteServer;
    }

    private function getInstallContext(InstallType $installType): Context
    {
        $install = Install::where('type', '=', $installType->value)
            ->where('group_id', '=', $this->instance->group->id)
            ->currentStatus(InstallStatus::FINISHED)
            ->first();

        if (empty($install)) {
            throw new LogicException(sprintf('No valid %s install was found!', $installType->value));
        }

        $installContext = new Context($install->resulting_context);

        return $installContext;
    }
}
