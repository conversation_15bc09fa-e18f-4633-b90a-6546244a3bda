<?php

namespace App\System\Installs\Stages\Traits;

use App\System\Installs\Stages\Data\StageScope;
use App\System\Runtime\Context;
use LogicException;
use RuntimeException;

trait InteractsWithContext
{
    private Context|null $context;
    private StageScope|null $scope = null;

    public function provideContext(Context $context): static
    {
        $this->context = $context;

        return $this;
    }

    public function provideScope(StageScope|null $scope): static
    {
        $this->scope = $scope;

        return $this;
    }

    public function getScope(): StageScope|null
    {
        return $this->scope;
    }

    public function set(string $key, mixed $value): static
    {
        // TODO think about writing into context using refs as well.

        $key = ! empty($this->scope) ? implode('.', [$this->scope->main, $key]) : $key;
        $this->globalSet($key, $value);

        return $this;
    }

    public function get(string $key, bool $required = true, mixed $default = null): mixed
    {
        if (! empty($this->scope)) {
            $refsKey = $this->scope->refs[$key] ?? null;

            if (empty($refsKey)) {
                $key = implode('.', [$this->scope->main, $key]);
            } else {
                $key = $refsKey;
            }
        }

        return $this->globalGet($key, $required, $default);
    }

    public function globalSet(string $key, mixed $value): static
    {
        if (! $this->context) {
            throw new LogicException('Context is not provided!');
        }

        $this->context->set($key, $value);

        return $this;
    }

    public function globalGet(string $key, bool $required = true, mixed $default = null): mixed
    {
        if (! $this->context) {
            throw new LogicException('Context is not provided!');
        }

        if ($required && ! $this->context->has($key)) {
            throw new RuntimeException(sprintf('Context missing required "%s" value!', $key));
        }

        $value = $this->context->get($key);

        if ($value === null) {
            return $default;
        }

        return $value;
    }
}
