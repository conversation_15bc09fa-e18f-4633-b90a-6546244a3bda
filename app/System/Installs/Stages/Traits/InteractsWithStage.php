<?php

namespace App\System\Installs\Stages\Traits;

use App\Models\Installs\Stage;
use App\System\Installs\Stages\Data\StageLogType;
use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Contracts\Command;
use Closure;
use Illuminate\Support\Str;
use LogicException;
use Throwable;

trait InteractsWithStage
{
    private Stage|null $stage = null;
    private string|null $name = null;
    private bool $disableLogging = false;

    public function getClass(): string
    {
        return self::class;
    }

    public function logCommandResultsToStage(): void
    {
        app('command.hub')->beforeCommand(function (Command $command) {
            if ($this->disableLogging) {
                return;
            }

            $this->log(
                StageLogType::INFO,
                sprintf('Executing %s command:', $command->getType()->value),
                $command->getPayload(),
            );
        });

        app('command.hub')->afterCommand(function (CommandExecResult $result) {
            if ($this->disableLogging) {
                return;
            }

            if ($result->isSuccessfull()) {
                $this->log(StageLogType::INFO, 'Success! Result:', ['result_payload' => $result->payload]);
            } else {
                /**
                 * NOTE
                 * Command failing might not lead to ERROR state and stage termination, hence why
                 * we log such outcomes as WARNING type.
                 */

                $this->log(StageLogType::WARNING, 'Error! Result:', [
                    'payload' => $result->payload,
                    'error' => $result->error->toArray(),
                ]);
            }
        });
    }

    public function provideName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function linkWithStage(Stage $stage): void
    {
        $this->stage = $stage;
    }

    public function try(Closure $closure, bool $throw = true): mixed
    {
        try {
            return $closure();
        } catch (Throwable $e) {
            $this->log(StageLogType::ERROR, sprintf(
                "Error has occured! %s\n%s",
                get_class($e),
                Str::limit($e->getMessage(), 100),
            ));

            if ($throw) {
                throw $e;
            } else {
                report($e);
            }

            return null;
        }
    }

    public function runSilenced(Closure $closure): mixed
    {
        try {
            $this->disableLogging = true;
            $result = $closure();
            $this->disableLogging = false;

            return $result;
        } catch (Throwable $e) {
            $this->disableLogging = false;

            throw $e;
        }
    }

    public function log(StageLogType $type, string $content, array $data = []): void
    {
        if (empty($this->stage)) {
            return;
        }

        if (! empty($data)) {
            $content = $content . PHP_EOL . json_encode($data, JSON_PRETTY_PRINT);
        }

        $this->stage->logs()->create([
            'type' => $type,
            'content' => $content,
        ]);
    }

    public function assertStageIsLinked(): void
    {
        if (empty($this->stage)) {
            throw new LogicException('Assertion failed! Stage is not linked.');
        }
    }
}
