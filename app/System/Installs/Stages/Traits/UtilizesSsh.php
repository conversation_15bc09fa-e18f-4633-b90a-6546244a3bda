<?php

namespace App\System\Installs\Stages\Traits;

use App\Models\Infrastructure\Instance;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Exceptions\CommandFailedException;
use App\System\Runtime\Execs\SshCommandExec;
use RuntimeException;

trait UtilizesSsh
{
    private SshCommandExec|null $sshExec = null;
    private int $maxTriesToConnect = 10;

    private function initializeSshExec(string $host, string $user, string $privateKey): void
    {
        $this->sshExec = app(SshCommandExec::class, ['host' => $host, 'user' => $user, 'privateKey' => $privateKey]);
    }

    private function initializeSshExecFromInstance(Instance $instance): void
    {
        $sshConnection = $instance->sshConnections->first();

        if (! $sshConnection) {
            throw new RuntimeException(sprintf(
                'Provided instance %d does not have SSH connection stored!',
                $instance->id,
            ));
        }

        $this->initializeSshExec(
            $sshConnection->host,
            $sshConnection->user,
            $sshConnection->private_key,
        );
    }

    private function waitUntilSshConnectionIsAvailable(): void
    {
        $this->try(function () {
            $this->runSilenced(function () {
                $testCommand = new SshCommand('echo "Good to go!"');

                $tries = 0;
                while (true) {
                    try {
                        /** @var \App\System\Runtime\Commands\Data\CommandExecResult */
                        app('command.hub')->execute($testCommand, $this->sshExec);

                        return;
                    } catch (CommandFailedException $e) {
                        if ($tries > $this->maxTriesToConnect) {
                            throw $e;
                        }

                        sleep(5);
                    }

                    $tries++;
                }
            });
        });
    }
}
