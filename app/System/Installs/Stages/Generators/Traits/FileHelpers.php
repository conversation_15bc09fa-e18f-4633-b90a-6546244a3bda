<?php

namespace App\System\Installs\Stages\Generators\Traits;

trait FileHelpers
{
    private function replaceEnv(string $env, array $map): string
    {
        $result = $env;
        foreach ($map as $key => $value) {
            $escapedValue = $this->dockerComposeEscape((string) $value);
            $result = preg_replace("/^({$key})=[^\n]*$/m", "$1=\"{$escapedValue}\"", $result);
        }

        return $result;
    }

    private function echoEscape(string $value, bool $singleQuotes = false): string
    {
        if ($singleQuotes) {
            return str_replace("'", '\x27', $value);
        }

        return str_replace('"', '\x22', $value);
    }

    private function dockerComposeEscape(string $value): string
    {
        return str_replace('"', '\"', $value);
    }
}
