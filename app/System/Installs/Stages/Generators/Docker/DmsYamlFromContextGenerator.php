<?php

namespace App\System\Installs\Stages\Generators\Docker;

use App\Models\Versions\Version;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Generators\Traits\FileHelpers;

class DmsYamlFromContextGenerator extends BaseTemplateGenerator
{
    use FileHelpers;

    protected function bake(string $template, array $data): string
    {
        return $this->echoEscape($template);
    }

    protected function getTemplate(Contextable|null $context): string
    {
        // TODO refactor templates into separate classes with different strategies.
        $versionId = $context->get('version_id', required: false);
        if (! empty($versionId)) {
            $version = Version::find($versionId);

            if (! empty($version->docker_compose_yaml)) {
                return $version->docker_compose_yaml;
            }
        }

        return file_get_contents(resource_path('devops/dms/docker-compose.yaml'));
    }

    protected function getData(Contextable|null $context): array
    {
        return [
            //
        ];
    }
}
