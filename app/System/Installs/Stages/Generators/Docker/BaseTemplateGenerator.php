<?php

namespace App\System\Installs\Stages\Generators\Docker;

use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Contracts\TemplateGenerator;
use RuntimeException;

abstract class BaseTemplateGenerator implements TemplateGenerator
{
    public function generate(Contextable|null $context): string
    {
        $template = $this->getTemplate($context);

        if ($template === false) {
            throw new RuntimeException(sprintf(
                'Could not read template file "%s"!',
                $template,
            ));
        }

        return $this->bake($template, $this->getData($context));
    }

    abstract protected function bake(string $template, array $data): string;
    abstract protected function getTemplate(Contextable|null $context): string;
    abstract protected function getData(Contextable|null $context): array;
}
