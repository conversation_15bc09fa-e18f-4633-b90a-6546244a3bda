<?php

namespace App\System\Installs\Stages\Generators\Docker;

use App\Models\Versions\Version;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Generators\Traits\FileHelpers;

class DmsEnvFromContextGenerator extends BaseTemplateGenerator
{
    use FileHelpers;

    protected function bake(string $template, array $data): string
    {
        return $this->replaceEnv($template, $data);
    }

    protected function getTemplate(Contextable|null $context): string
    {
        // TODO refactor templates into separate classes with different strategies.
        $versionId = $context->get('version_id', required: false);
        if (! empty($versionId)) {
            $version = Version::find($versionId);

            if (! empty($version->docker_compose_env)) {
                return $version->docker_compose_env;
            }
        }

        return file_get_contents(resource_path('devops/dms/docker-compose.env'));
    }

    protected function getData(Contextable|null $context): array
    {
        $appName = $this->dockerComposeEscape($context->get('company_name'));

        return [
            'COMPOSE_PROJECT_NAME' => $context->get('compose_project_name'),
            'TAG' => $context->get('tag'),
            'REGISTRY' => $context->get('registry'),
            'VM_NAME' => $context->get('vm_name'),

            'DMS_HOST' => $context->get('subdomain'),
            'DMS_DOMAIN' => $context->get('domain'),
            'PWA_HOST' => $context->get('pwa_subdomain'),
            'PWA_DOMAIN' => $context->get('pwa_domain'),

            'ZBX_METADATA' => $context->get('zbx_metadata'),

            'MYSQL_ROOT_PASSWORD' => $context->get('mysql_root_password'),
            'DB_PASSWORD' => $context->get('mysql_password'),
            'REDIS_PASSWORD' => $context->get('redis_password'),

            'BROADCAST_DRIVER' => 'pusher',

            'MAIL_MAILER' => $context->get('mail_mailer'),
            'MAIL_HOST' => $context->get('mail_host'),
            'MAIL_PORT' => $context->get('mail_port'),
            'MAIL_USERNAME' => $context->get('mail_username'),
            'MAIL_PASSWORD' => $context->get('mail_password'),
            'MAIL_ENCRYPTION' => $context->get('mail_encryption'),
            'MAIL_FROM_ADDRESS' => $context->get('mail_from_address'),
            'MAIL_FROM_NAME' => $appName,

            'MARKETPLACES_ENCRYPTION_KEY' => $context->get('marketplaces_encryption_key'),

            'GOOGLE_MAP_API_KEY' => $context->get('google_maps_api_key'),

            'APP_NAME' => $appName,
            'APP_DEBUG' => 'false',
            'APP_KEY' => $context->get('app_key'),
            'APP_ENV' => $context->get('environment'),
            'APP_URL' => $context->get('url'),

            'ELASTICSEARCH_HOST' => $context->get('elasticsearch_host'),
            'ELASTICSEARCH_USER' => $context->get('elasticsearch_user', required: false),
            'ELASTICSEARCH_PASSWORD' => $context->get('elasticsearch_password', required: false),

            'PDF_BASE_URL' => $context->get('pdf_base_url'),

            'PUSHER_APP_KEY' => $context->get('pusher_app_key'),
            'PUSHER_APP_SECRET' => $context->get('pusher_app_secret'),
            'PUSHER_APP_ID' => $context->get('pusher_app_id'),
            'VITE_PUSHER_APP_KEY' => $context->get('pusher_app_key'),

            'CHECKER_BASE_URL' => $context->get('checker_base_url'),
            'CHECKER_CLIENT_ID' => $context->get('checker_client_id'),
            'CHECKER_CLIENT_SECRET' => $context->get('checker_client_secret'),

            'AI_SERVICE_BASE_URL' => $context->get('ai_service_base_url'),
            'AI_SERVICE_TOKEN' => $context->get('ai_service_api_token'),

	        'SCRM_BASE_URL' => $context->get('scrm_base_url'),
	        'SCRM_CLIENT_ID' => $context->get('scrm_client_id'),
	        'SCRM_CLIENT_SECRET' => $context->get('scrm_client_secret'),
	        'SCRM_GRANT_TYPE' => $context->get('scrm_grant_type'),
	        'SCRM_SCOPE' => $context->get('scrm_scope'),

            'ENVELOPE_BASE_URL' => $context->get('envelope_base_url'),
            'ENVELOPE_APP_ID' => $context->get('envelope_app_id'),
            'ENVELOPE_AUTH_KEY_ID' => $context->get('envelope_auth_key_id'),
            'ENVELOPE_AUTH_KEY_SECRET' => $context->get('envelope_auth_key_secret'),

            'EMAIL' => $context->get('register_admin_email'),
            'COMPANY_NAME' => $context->get('company_name'),
            'HAS_PWA' => (int) $context->get('with_pwa'),

            'AWS_BUCKET' => $context->get('aws_bucket'),
            'AWS_ACCESS_KEY_ID' => $context->get('aws_access_key_id'),
            'AWS_SECRET_ACCESS_KEY' => $context->get('aws_access_secret_key'),
            'AWS_URL' => $context->get('aws_url'),

            'SENTRY_LARAVEL_DSN' => $context->get('sentry_dsn'),
            'SENTRY_RELEASE' => $context->get('sentry_release', required: false),
            'SENTRY_ENVIRONMENT' => $context->get('sentry_environment', required: false),
        ];
    }
}
