<?php

namespace App\System\Installs\Stages\Generators\Docker;

use App\Models\Versions\Version;
use App\System\Installs\Stages\Contracts\Contextable;
use App\System\Installs\Stages\Generators\Traits\FileHelpers;

class PwaEnvFromContextGenerator extends BaseTemplateGenerator
{
    use FileHelpers;

    protected function bake(string $template, array $data): string
    {
        return $this->replaceEnv($template, $data);
    }

    protected function getTemplate(Contextable|null $context): string
    {
        // TODO refactor templates into separate classes with different strategies.
        $versionId = $context->get('version_id', required: false);
        if (! empty($versionId)) {
            $version = Version::find($versionId);

            if (! empty($version->docker_compose_env)) {
                return $version->docker_compose_env;
            }
        }

        return file_get_contents(resource_path('devops/pwa/docker-compose.env'));
    }

    protected function getData(Contextable|null $context): array
    {
        return [
            'COMPOSE_PROJECT_NAME' => $context->get('compose_project_name'),
            'TAG' => $context->get('tag'),
            'REGISTRY' => $context->get('registry'),
            'VM_NAME' => $context->get('vm_name'),
            'PWA_HOSTNAME' => $context->get('hostname'),

            'APP_ID' => $context->get('app_id'),
            'APP_SECRET_KEY' => $context->get('app_secret_key'),
            'APP_NAME' => $this->dockerComposeEscape($context->get('name')),
            'APP_KEY' => $context->get('app_key'),
            'APP_ENV' => $context->get('environment'),
            'APP_DEBUG' => false,
            'APP_URL' => $context->get('url'),
            'BASE_DOMAIN' => $context->get('zone_name'),

            'APP_ADMIN_URL' => $context->get('app_admin_url'),
            'APP_ADMIN_CLIENT_ID' => $context->get('app_admin_client_id'),
            'APP_ADMIN_SECRET' => $context->get('app_admin_secret'),

            'DMS_HOST' => $context->get('dms_url'),
            'DMS_CLIENT_ID' => $context->get('dms_client_id'),
            'DMS_CLIENT_SECRET' => $context->get('dms_client_secret'),
            'DMS_PUSHER_APP_KEY' => $context->get('dms_pusher_app_key'),
            'DMS_PUSHER_APP_SECRET' => $context->get('dms_pusher_app_secret'),

            'MAIL_MAILER' => $context->get('mail_mailer'),
            'MAIL_HOST' => $context->get('mail_host'),
            'MAIL_PORT' => $context->get('mail_port'),
            'MAIL_USERNAME' => $context->get('mail_username'),
            'MAIL_PASSWORD' => $context->get('mail_password'),
            'MAIL_ENCRYPTION' => $context->get('mail_encryption'),
            'MAIL_FROM_ADDRESS' => $context->get('mail_from_address'),
            'MAIL_FROM_NAME' => $context->get('mail_from_name'),

            'VITE_DMS_HOST' => $context->get('dms_url'),
            'VITE_DMS_PUSHER_APP_KEY' => $context->get('dms_pusher_app_key'),

            'VAPID_PUBLIC_KEY' => $context->get('vapid_public_key'),
            'VAPID_PRIVATE_KEY' => $context->get('vapid_private_key'),

            'SENTRY_LARAVEL_DSN' => $context->get('sentry_dsn'),
            'SENTRY_RELEASE' => $context->get('sentry_release', required: false),
            'SENTRY_ENVIRONMENT' => $context->get('sentry_environment', required: false),
        ];
    }
}
