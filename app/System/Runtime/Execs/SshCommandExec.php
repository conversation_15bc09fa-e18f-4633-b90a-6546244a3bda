<?php

namespace App\System\Runtime\Execs;

use App\Models\Infrastructure\Connections\SshConnection;
use App\System\Runtime\Commands\Data\CommandExecError;
use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Commands\SshCommand;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Contracts\CommandExec;
use Closure;
use InvalidArgumentException;
use LogicException;
use Spatie\Ssh\Ssh;
use Symfony\Component\Process\Process;

class SshCommandExec implements CommandExec
{
    private Ssh|null $client = null;
    private int|null $timeout = null;
    private bool $removeBash = false;
    private Closure|null $onOutput = null;

    public function __construct(
        private string $host,
        private string $user,
        private string $privateKey,
    ) {
        //
    }

    public static function createFromModel(SshConnection $model): static
    {
        return (new static($model->host, $model->user, $model->private_key))
            ->setTimeout($model->timeout)
            ->removeBash($model->remove_bash);
    }

    public function getHost(): string
    {
        return $this->host;
    }

    public function getUser(): string
    {
        return $this->user;
    }

    public function getPrivateKey(): string
    {
        return $this->privateKey;
    }

    public function setOnOutput(Closure|null $onOutput): static
    {
        $this->onOutput = $onOutput;

        return $this;
    }

    public function setTimeout(int|null $timeout): static
    {
        if ($timeout !== null && $timeout <= 0) {
            throw new InvalidArgumentException('SSH timeout must be a positive int if set!');
        }

        $this->timeout = $timeout;

        return $this;
    }

    public function getTimeout(): int|null
    {
        return $this->timeout;
    }

    public function removeBash(bool $value = true): static
    {
        $this->removeBash = $value;

        return $this;
    }

    public function getRemoveBash(): bool
    {
        return $this->removeBash;
    }

    public function execute(Command $command): CommandExecResult
    {
        if (! $command instanceof SshCommand) {
            throw new LogicException('SSH command executor does not support provided command type!');
        }

        $this->initializeClientIfNeeded();

        $process = $this->client->execute($command->script);

        $output = $process->getIncrementalOutput();
        $errorMessage = $process->getIncrementalErrorOutput();
        $exitCode = $process->getExitCode();

        if ($exitCode !== 0) {
            $error = new CommandExecError($errorMessage ?? 'No error output', $exitCode);
        }

        return new CommandExecResult(
            ['stdout' => $output],
            $error ?? null,
        );
    }

    private function initializeClientIfNeeded(): void
    {
        if (! $this->client) {
            $this->client = app(Ssh::class, ['user' => $this->user, 'host' => $this->host])
                ->configureProcess(fn (Process $process) => $process->setTimeout($this->timeout))
                ->usePrivateKey($this->privateKey)
                ->disableStrictHostKeyChecking()
                ->disablePasswordAuthentication();

            if (! empty($this->onOutput)) {
                $this->client->onOutput($this->onOutput);
            }

            if ($this->removeBash) {
                $this->client->removeBash();
            }
        }
    }
}
