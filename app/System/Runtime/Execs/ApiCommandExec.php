<?php

namespace App\System\Runtime\Execs;

use App\System\Runtime\Commands\ApiCommand;
use App\System\Runtime\Commands\Data\ApiMethod;
use App\System\Runtime\Commands\Data\CommandExecError;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Contracts\CommandExec;
use App\System\Runtime\Commands\Data\CommandExecResult;
use Illuminate\Support\Facades\Http;
use LogicException;

class ApiCommandExec implements CommandExec
{
    public function execute(Command $command): CommandExecResult
    {
        if (! $command instanceof ApiCommand) {
            throw new LogicException('API command executor does not support provided command type!');
        }

        $pending = Http::withHeaders($command->headers);

        if (! empty($command->auth)) {
            $pending->withBasicAuth($command->auth->user, $command->auth->password);
        }

        $response = match ($command->method) {
            ApiMethod::GET => $pending->get($command->url, $command->data),
            ApiMethod::POST => $pending->post($command->url, $command->data),
            ApiMethod::DELETE => $pending->delete($command->url, $command->data),
            default => throw new LogicException('Method not implemented!'),
        };

        if ($response->failed()) {
            $error = new CommandExecError(
                'HTTP Error',
                $response->status(),
                ! empty($response->json()) ? $response->json() : [],
            );
        }

        return new CommandExecResult($response->json(), $error ?? null);
    }
}
