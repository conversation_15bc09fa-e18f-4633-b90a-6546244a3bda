<?php

namespace App\System\Runtime;

use Illuminate\Support\Arr;
use InvalidArgumentException;

class Context
{
    /**
     * @param string[] $values
     */
    public function __construct(private array $values = [])
    {
        foreach ($values as $key => $value) {
            if (! is_string($key)) {
                throw new InvalidArgumentException('Context requires explicit string keys!');
            }

            $this->set($key, $value);
        }
    }

    public function update(array $values): static
    {
        $this->values = array_merge($this->values, $values);

        return $this;
    }

    public function has(string $key): bool
    {
        $value = Arr::get($this->values, $key, null);

        return $value !== null;
    }

    public function set(string $key, array|int|float|string|bool|null $value): static
    {
        Arr::set($this->values, $key, $value);

        return $this;
    }

    public function get(string $key): array|int|float|string|bool|null
    {
        return Arr::get($this->values, $key);
    }

    public function toArray(): array
    {
        return $this->values;
    }
}
