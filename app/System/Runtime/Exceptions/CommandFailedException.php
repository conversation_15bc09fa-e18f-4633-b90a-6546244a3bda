<?php

namespace App\System\Runtime\Exceptions;

use App\Models\Logs\CommandLog;
use App\System\Runtime\Commands\Data\CommandExecError;
use Exception;

class CommandFailedException extends Exception
{
    public function __construct(
        public readonly CommandExecError $error,
        public readonly CommandLog $log,
    ) {
        if ($log->model) {
            parent::__construct(sprintf(
                'Command failed with error: "%s". See command log #%d, model %s %d.',
                $error->message,
                $log->id,
                $log->model->getMorphClass(),
                $log->model->id,
            ));

            return;
        }

        parent::__construct(sprintf(
            'Command failed with error: "%s". See command log #%d.',
            $error->message,
            $log->id,
        ));
    }
}
