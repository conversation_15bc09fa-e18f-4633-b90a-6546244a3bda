<?php

namespace App\System\Runtime\IO\Exceptions;

use App\System\Runtime\IO\Data\InputData;
use App\System\Runtime\IO\Data\InputDataBag;
use Exception;

class InputDataDuplicationException extends Exception
{
    public function __construct(
        public readonly InputDataBag $inputDataBag,
        public readonly InputData $existing,
        public readonly InputData $new,
    ) {
        parent::__construct(sprintf('This input bag already has input data named `%s`!', $existing->name));
    }
}
