<?php

namespace App\System\Runtime\IO\Data;

use App\System\Runtime\IO\Exceptions\InputDataDuplicationException;

class InputDataBag
{
    /** @var \App\System\IO\Data\InputData[] */
    private array $items;

    public function __construct(InputData ...$items)
    {
        $this->items = [];

        foreach ($items as $item) {
            $this->add($item);
        }
    }

    public static function empty(): static
    {
        return new static();
    }

    /**
     * @return \App\System\IO\Data\InputData[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    public function isEmpty(): bool
    {
        return empty($this->items);
    }

    private function add(InputData $inputData): void
    {
        if (array_key_exists($inputData->name, $this->items)) {
            throw new InputDataDuplicationException($this, $this->items[$inputData->name], $inputData);
        }

        $this->items[$inputData->name] = $inputData;
    }
}
