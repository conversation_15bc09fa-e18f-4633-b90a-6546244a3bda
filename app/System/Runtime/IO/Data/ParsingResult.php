<?php

namespace App\System\Runtime\IO\Data;

use LogicException;

class ParsingResult
{
    public function __construct(
        public readonly string|null $value,
        public readonly string|null $errorText,
    ) {
        if (! empty($value) && ! empty($errorText)) {
            throw new LogicException('Cannot have both value and error as parsing result!');
        }
    }

    public static function empty(): static
    {
        return new static(null, null);
    }

    public function isSuccessfull(): bool
    {
        return $this->errorText === null;
    }
}
