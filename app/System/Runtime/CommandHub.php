<?php

namespace App\System\Runtime;

use App\Models\Logs\CommandLog;
use App\System\Runtime\Commands\Data\CommandExecResult;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Contracts\CommandExec;
use App\System\Runtime\Exceptions\CommandFailedException;
use Closure;
use Illuminate\Database\Eloquent\Model;
use LogicException;
use Throwable;

class CommandHub
{
    private Model|null $context = null;
    private Closure|null $beforeCommand = null;
    private Closure|null $afterCommand = null;

    public function clearCommandHooks(): static
    {
        $this->beforeCommand = null;
        $this->afterCommand = null;

        return $this;
    }

    public function addEloquentContext(Model $context): static
    {
        $this->context = $context;

        return $this;
    }

    public function beforeCommand(Closure|null $closure): static
    {
        $this->beforeCommand = $closure;

        return $this;
    }

    public function afterCommand(Closure|null $closure): static
    {
        $this->afterCommand = $closure;

        return $this;
    }

    public function execute(Command $command, CommandExec|null $using = null): CommandExecResult
    {
        $record = new CommandLog([
            'type' => $command->getType(),
            'payload' => $command->getPayload(),
            'has_errors' => false,
        ]);
        if ($this->context) {
            $record->model()->associate($this->context);
        }
        $record->save();

        try {
            $exec = $using ?: $command->getDefaultExec();
            if (! $exec) {
                throw new LogicException('No default exec exists for command! Provide command exec explicitly!');
            }

            if ($this->beforeCommand) {
                ($this->beforeCommand)($command);
            }

            $result = $exec->execute($command);

            if ($this->afterCommand) {
                ($this->afterCommand)($result);
            }
        } catch (Throwable $e) {
            $record->error = ['message' => $e->getMessage(), 'code' => $e->getCode()];
            $record->save();

            throw $e;
        }

        $record->result = $result->payload;
        $record->error = $result->error
            ? ['message' => $result->error->message, 'code' => $result->error->code, 'data' => $result->error->data]
            : null;

        $record->save();

        if (! $result->isSuccessfull()) {
            throw new CommandFailedException($result->error, $record);
        }

        return $result;
    }
}
