<?php

namespace App\System\Runtime\Commands;

use App\System\Runtime\Commands\Data\ApiMethod;
use App\System\Runtime\Commands\Data\BasicAuth;
use App\System\Runtime\Commands\Data\CommandType;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Contracts\CommandExec;
use App\System\Runtime\Execs\ApiCommandExec;

class ApiCommand implements Command
{
    public function __construct(
        public readonly string $url,
        public readonly ApiMethod $method,
        public readonly array $data,
        public readonly array $headers = [],
        public readonly BasicAuth|null $auth = null,
    ) {
        //
    }

    public function getType(): CommandType
    {
        return CommandType::API;
    }

    public function getPayload(): array
    {
        return [
            'url' => $this->url,
            'method' => $this->method->value,
            'data' => $this->data,
            'headers' => $this->headers,
            'auth' => $this->auth?->toArray(),
        ];
    }

    public function getDefaultExec(): CommandExec|null
    {
        return app(ApiCommandExec::class);
    }

    public function updateHeaders(array $newHeaders): static
    {
        return new ApiCommand($this->url, $this->method, $this->data, array_merge($this->headers, $newHeaders));
    }
}
