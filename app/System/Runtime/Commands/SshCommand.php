<?php

namespace App\System\Runtime\Commands;

use App\System\Runtime\Commands\Data\CommandType;
use App\System\Runtime\Contracts\Command;
use App\System\Runtime\Contracts\CommandExec;

class SshCommand implements Command
{
    public function __construct(public readonly string $script)
    {
        //
    }

    public function getType(): CommandType
    {
        return CommandType::SSH;
    }

    public function getPayload(): array
    {
        return [
            'script' => $this->script,
        ];
    }

    public function getDefaultExec(): CommandExec|null
    {
        return null;
    }
}
