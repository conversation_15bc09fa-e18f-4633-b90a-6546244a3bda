<?php

namespace App\System\Runtime\Commands\Data;

class CommandExecError
{
    public function __construct(
        public readonly string $message,
        public readonly int $code,
        public readonly array $data = [],
    ) {
        //
    }

    public function toArray(): array
    {
        return [
            'message' => $this->message,
            'code' => $this->code,
            'data' => $this->data,
        ];
    }
}
