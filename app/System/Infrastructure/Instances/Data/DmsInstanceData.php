<?php

namespace App\System\Infrastructure\Instances\Data;

use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Contracts\InstanceData;
use App\System\Infrastructure\Instances\Exceptions\MissingOauthClientException;
use App\System\Infrastructure\Instances\Exceptions\OauthClientAlreadyExistsException;
use InvalidArgumentException;

class DmsInstanceData implements InstanceData
{
    public function __construct(
        public readonly string $name,
        public readonly string $subdomain,
        public readonly string $hostname,
        public readonly string $url,
        public readonly Hosting $hosting,
        public readonly string $ip,
        public readonly string $externalId,
        public readonly string $externalName,
        public readonly array $oauthClients,
        public readonly PusherData $pusherData,
    ) {
        foreach ($oauthClients as $oauthClient) {
            if (! $oauthClient instanceof OauthClient) {
                throw new InvalidArgumentException('Each of $oauthClients must be an OauthClient!');
            }
        }
    }

    public function mergeOauthClient(string $id, OauthClient $oauthClient, bool $override = true): static
    {
        if (array_key_exists($id, $this->oauthClients) && ! $override) {
            throw new OauthClientAlreadyExistsException($id);
        }

        $oauthClients = $this->oauthClients;
        $oauthClients[$id] = $oauthClient;

        return new DmsInstanceData(
            $this->name,
            $this->subdomain,
            $this->hostname,
            $this->url,
            $this->hosting,
            $this->ip,
            $this->externalId,
            $this->externalName,
            $oauthClients,
            $this->pusherData,
        );
    }

    public function getOauthClientById(string $id): OauthClient
    {
        if (! array_key_exists($id, $this->oauthClients)) {
            throw new MissingOauthClientException($id);
        }

        return $this->oauthClients[$id];
    }

    public function for(): AppType
    {
        return AppType::DMS;
    }

    public function getData(): array
    {
        return [
            'name' => $this->name,
            'subdomain' => $this->subdomain,
            'hostname' => $this->hostname,
            'url' => $this->url,
            'hosting' => $this->hosting->value,
            'ip' => $this->ip,
            'external_id' => $this->externalId,
            'external_name' => $this->externalName,

            'oauth' => collect($this->oauthClients)->map(fn (OauthClient $oauthClient) => [
                'client_id' => $oauthClient->clientId,
                'client_secret' => $oauthClient->clientSecret,
            ])->all(),

            'pusher' => [
                'app_key' => $this->pusherData->appKey,
                'app_id' => $this->pusherData->appId,
                'app_secret' => $this->pusherData->appSecret,
            ]
        ];
    }
}
