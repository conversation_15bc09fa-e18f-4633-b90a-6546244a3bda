<?php

namespace App\System\Infrastructure\Instances\Data;

use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Contracts\InstanceData;
use App\System\Infrastructure\Instances\Exceptions\MissingOauthClientException;
use InvalidArgumentException;

class PwaInstanceData implements InstanceData
{
    public function __construct(
        public readonly string $name,
        public readonly string $subdomain,
        public readonly string $url,
        public readonly array $oauthClients,
    ) {
        foreach ($oauthClients as $oauthClient) {
            if (! $oauthClient instanceof OauthClient) {
                throw new InvalidArgumentException('Each of $oauthClients must be an OauthClient!');
            }
        }
    }

    public function getOauthClientById(string $id): OauthClient
    {
        if (! array_key_exists($id, $this->oauthClients)) {
            throw new MissingOauthClientException($id);
        }

        return $this->oauthClients[$id];
    }

    public function for(): AppType
    {
        return AppType::PWA;
    }

    public function getData(): array
    {
        return [
            'name' => $this->name,
            'subdomain' => $this->subdomain,
            'url' => $this->url,
            'oauth' => collect($this->oauthClients)->map(fn (OauthClient $oauthClient) => [
                'client_id' => $oauthClient->clientId,
                'client_secret' => $oauthClient->clientSecret,
            ])->all(),
        ];
    }
}
