<?php

namespace App\System\Infrastructure\Instances\Factories;

use App\System\Apps\Data\AppType;
use App\System\Infrastructure\Instances\Contracts\InstanceData;
use App\System\Infrastructure\Instances\Data\DmsInstanceData;
use App\System\Infrastructure\Instances\Data\Hosting;
use App\System\Infrastructure\Instances\Data\OauthClient;
use App\System\Infrastructure\Instances\Data\PusherData;
use App\System\Infrastructure\Instances\Data\PwaInstanceData;
use LogicException;

class InstanceDataFactory
{
    public function createForAppFromData(AppType $appType, array $data): InstanceData
    {
        return match ($appType) {
            AppType::DMS => $this->createDmsInstanceData($data),
            AppType::PWA => $this->createPwaInstanceData($data),

            default => throw new LogicException(sprintf(
                'Instance data for app type "%s" is not implemented!',
                $appType->value,
            )),
        };
    }

    private function createDmsInstanceData(array $data): DmsInstanceData
    {
        $oauthClients = collect($data['oauth'])->map(
            fn (array $raw) => new OauthClient($raw['client_id'], $raw['client_secret']),
        )->all();

        $pusherData = new PusherData(
            $data['pusher']['app_key'],
            $data['pusher']['app_id'],
            $data['pusher']['app_secret'],
        );

        return new DmsInstanceData(
            $data['name'],
            $data['subdomain'],
            $data['hostname'],
            $data['url'],
            Hosting::from($data['hosting']),
            $data['ip'],
            $data['external_id'],
            $data['external_name'],
            $oauthClients,
            $pusherData,
        );
    }

    private function createPwaInstanceData(array $data): PwaInstanceData
    {
        $oauthClients = collect($data['oauth'])->map(
            fn (array $raw) => new OauthClient($raw['client_id'], $raw['client_secret']),
        )->all();

        return new PwaInstanceData(
            $data['name'],
            $data['subdomain'],
            $data['url'],
            $oauthClients,
        );
    }
}
