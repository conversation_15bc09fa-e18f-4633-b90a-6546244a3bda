<?php

namespace App\System\Versions\Data;

enum VersionStatus: string
{
    case PENDING = 'pending';
    case AVAILABLE = 'available';
    case DISABLED = 'disabled';

    public function validNextStatuses(): array
    {
        switch ($this) {
            case self::PENDING:
                return [self::AVAILABLE];
                // no break
            case self::AVAILABLE:
                return [self::DISABLED];
            case self::DISABLED:
                return [self::AVAILABLE];
        }

        return [];
    }
}
