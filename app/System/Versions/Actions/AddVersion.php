<?php

namespace App\System\Versions\Actions;

use App\Models\Versions\Version;
use App\System\Apps\Data\AppType;
use App\System\Versions\Actions\Data\OrderingStrategy;
use App\System\Versions\Actions\Exceptions\VersionAlreadyExistsException;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use LogicException;

class AddVersion
{
    private string $tag;
    private string $name;
    private string|null $description = null;
    private string|null $yaml = null;
    private string|null $env = null;
    private AppType $appType;
    private OrderingStrategy $orderingStrategy = OrderingStrategy::AUTO_INCREMENT;
    private int|null $order = null;

    public function __construct()
    {
        //
    }

    public function provideOrderingStrategy(OrderingStrategy $orderingStrategy, int|null $order = null): static
    {
        if ($orderingStrategy === OrderingStrategy::MANUAL) {
            if ($order === null) {
                throw new InvalidArgumentException('Order value is required for MANUAL strategy!');
            }

            if ($order <= 0) {
                throw new InvalidArgumentException('Only positive order values are supported!');
            }

            $this->order = $order;
        } else {
            $this->order = null;
        }

        $this->orderingStrategy = $orderingStrategy;

        return $this;
    }

    public function provideDockerComposeConfigs(string|null $yaml, string|null $env, bool $base64 = false): static
    {
        if ($base64) {
            $decodedYaml = base64_decode($yaml, strict: true);
            $decodedEnv = base64_decode($env, strict: true);

            if ($decodedYaml === false || $decodedEnv === false) {
                throw new InvalidArgumentException(
                    'Failed to decode provided base64 contents for docker compose configs!',
                );
            }

            $this->yaml = $decodedYaml;
            $this->env = $decodedEnv;
        } else {
            $this->yaml = $yaml;
            $this->env = $env;
        }

        return $this;
    }

    public function provideTag(string $tag): static
    {
        $this->tag = $tag;

        return $this;
    }

    public function provideTexts(string $name, string|null $description): static
    {
        $this->name = $name;
        $this->description = $description;

        return $this;
    }

    public function provideAppType(AppType $appType): static
    {
        $this->appType = $appType;

        return $this;
    }

    public function execute(): Version
    {
        return DB::transaction(function () {
            $order = $this->getNewVersionOrder();

            $version = new Version();
            $version->app_type = $this->appType;
            $version->order = $order;
            $version->tag = $this->tag;
            $version->name = $this->name;
            $version->description = $this->description;
            $version->docker_compose_yaml = $this->yaml;
            $version->docker_compose_env = $this->env;

            return tap($version)->save();
        });
    }

    private function getNewVersionOrder(): int
    {
        if ($this->orderingStrategy === OrderingStrategy::AUTO_INCREMENT) {
            Version::where('app_type', '=', $this->appType->value)->lockForUpdate()->get();
            $maxOrder = DB::table('versions')->where('app_type', '=', $this->appType)->max('order');

            if (empty($maxOrder)) {
                return 1;
            }

            return $maxOrder + 1;
        }

        if ($this->orderingStrategy === OrderingStrategy::MANUAL) {
            $existingVersion = Version::where('order', '=', $this->order)->lockForUpdate()->get();
            if (! $existingVersion) {
                return $this->order;
            }

            throw new VersionAlreadyExistsException($existingVersion, $this->order);
        }

        throw new LogicException('Unsupported ordering strategy for new version!');
    }
}
