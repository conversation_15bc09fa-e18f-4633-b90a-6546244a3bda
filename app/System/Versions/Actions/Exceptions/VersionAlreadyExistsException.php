<?php

namespace App\System\Versions\Actions\Exceptions;

use App\Models\Versions\Version;
use Exception;

class VersionAlreadyExistsException extends Exception
{
    public function __construct(
        public readonly Version $existingVersion,
        public readonly int $desiredOrder,
    ) {
        parent::__construct(sprintf(
            'Version with desired order %d already exists (ID "%s")!',
            $desiredOrder,
            $existingVersion->id,
        ));
    }
}
