<?php

namespace App\Console\Commands\Support;

use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallType;
use Illuminate\Console\Command;
use App\Jobs\Installs\RunInstall;

class AddEnvelopeToDmsCommand extends Command
{
    protected $signature = 'app:add-envelope-to-dms {dms_id}';
    protected $description = 'Add Envelope app to an existing DMS instance';

    public function handle(): int
    {
        $dmsID = $this->argument('dms_id');

        if (!is_numeric($dmsID)) {
            $this->error('Invalid DMS ID. It must be numeric.');
            return 1;
        }

        $this->info("Starting Envelope registration for DMS ID: $dmsID");

        try {
            $this->startAddEnvelopeToOldDms((int) $dmsID);
        } catch (\Throwable $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }

        $this->info("Envelope app successfully added.");
        return 0;
    }

    private function startAddEnvelopeToOldDms(int $dmsID): void
    {
        $payload = new InstallPayload(['dms_id' => $dmsID], null);

        $result = (new StartInstall())
            ->providePayload($payload)
            ->provideType(InstallType::ADD_ENVELOPE_TO_OLD_DMS)
            ->execute();

        RunInstall::dispatch($result->install);
    }
}
