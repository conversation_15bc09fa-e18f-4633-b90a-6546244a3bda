<?php

namespace App\Console\Commands\Support;

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class RegisterUser extends Command
{
    protected $signature = 'app:support:register-user {name} {email} {password}';
    protected $description = 'Register new user';

    public function handle()
    {
        $validator = Validator::make($this->arguments(), [
            'name' => ['required', 'min:3'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'min:6'],
        ]);

        if ($validator->fails()) {
            $this->error("Validation error(s)!");
            $this->error($validator->errors()->toJson());

            return 1;
        }

        $user = User::create([
            'name' => $this->argument('name'),
            'email' => $this->argument('email'),
            'password' => Hash::make($this->argument('password')),
        ]);

        event(new Registered($user));

        $this->info(sprintf('Successfully registered new user `%s`', $this->argument('email')));
    }
}
