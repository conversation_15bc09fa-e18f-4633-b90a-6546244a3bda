<?php

namespace App\Console\Commands;

use App\Models\Installs\Install;
use App\Models\Installs\Stage;
use App\System\Installs\Actions\RunInstall;
use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Stages\Contracts\StageImplementation;
use App\System\Installs\Stages\Implementations\Hetzner\Support\Firewalls;
use App\System\Runtime\Context;
use Illuminate\Console\Command;
use Throwable;

class Sandbox extends Command
{
    protected $signature = 'app:sandbox';
    protected $description = 'Test';

    public function handle()
    {
        //$api->retrieveProject('sentry', 'localtest');
        //$api->createProject('sentry', 'dilizy-dev-hetzner', 'localtest', 'php-laravel');

        //$this->startInstall();
        //$this->testFirewallDeletion();

        //$this->createInstallDms();
        //$this->createInstallPwa();
        //$this->runInstall(31);

        //$this->startDmsInstall();
        //$this->startPwaInstall();

        //$this->runStage(98);
    }

    private function runInstall($id)
    {
        $install = Install::find($id);

        $runInstall = new RunInstall($install);
        $runInstall->execute();
    }

    private function createInstallDms()
    {
        $startInstall = new StartInstall();
        $startInstall->provideType(InstallType::BASIC_DMS_SETUP);
        $startInstall->providePayload(new InstallPayload([
            'subdomain' => 'dmspwa6',
            'email' => '<EMAIL>',
            'company_name' => 'Test Company',
            'with_pwa' => true,
        ], null));
        $result = $startInstall->execute();
    }

    private function startDmsInstall()
    {
        $startInstall = new StartInstall();
        $startInstall->provideType(InstallType::BASIC_DMS_SETUP);
        $startInstall->providePayload(new InstallPayload([
            'subdomain' => 'dmspwa11',
            'email' => '<EMAIL>',
            'company_name' => 'Test Company',
            'with_pwa' => true,
        ], null));
        $result = $startInstall->execute();

        $runInstall = new RunInstall($result->install);
        $runInstall->execute();
    }

    private function createInstallPwa()
    {
        $startInstall = new StartInstall();
        $startInstall->provideType(InstallType::PWA_FOR_DMS_SETUP);
        $startInstall->providePayload(new InstallPayload(['dms_id' => 14], null));

        $result = $startInstall->execute();
    }

    private function startPwaInstall()
    {
        $startInstall = new StartInstall();
        $startInstall->provideType(InstallType::PWA_FOR_DMS_SETUP);
        $startInstall->providePayload(new InstallPayload(['dms_id' => 22], null));

        $result = $startInstall->execute();

        $runInstall = new RunInstall($result->install);
        $runInstall->execute();
    }

    private function testFirewallDeletion()
    {
        $contextData = json_decode(<<<EOD
 {
    "dms": {
        "group_id": 3,
        "ssh_user": "root",
        "server_id": 48123823,
        "firewall_id": 1421818,
        "instance_id": 3,
        "private_key": "/home/<USER>/.ssh/ideil_wah_development",
        "server_name": "dilizy-dev-envtest",
        "server_type": "cx21",
        "server_image": "docker-ce",
        "new_group_name": "basic-dms-setup-envtest",
        "server_public_ip": "************",
        "server_private_ip": null,
        "server_ssh_key_file": "/home/<USER>/.ssh/ideil_wah_development",
        "server_location_city": "Falkenstein",
        "server_network_names": [
            "dilizy-dev-private"
        ],
        "server_ssh_key_names": [
            "ideil",
            "vkulyk_mindk",
            "dsydorenko"
        ],
        "elasticsearch_vm_name": "dilizy-dev-service",
        "server_firewall_names": [
            "dilizy-dev-vms"
        ],
        "elasticsearch_server_id": 45921341
    },
    "dns": {
        "subdomain": "envtest",
        "zone_name": "dilizy.dev",
        "public_record_id": "e9d276959c35a32246baec9608430cc6"
    },
    "checker_for_dms": {
        "host": "**************",
        "user": "ideil",
        "app_name": "envtest",
        "base_url": "https://checker.additional.dev.dilizy.dev",
        "client_id": "52",
        "private_key": "/home/<USER>/.ssh/ideil_wah_development",
        "client_secret": "vzYzy7V4Lo3bn5HLxbRU1UwRiRF6YNVXdPET5fMC",
        "container_name": "checker-checker-1"
    },
    "dms_docker_compose": {
        "tag": "feature-68630-test-deploy-env-a121fc3d",
        "url": "https://envtest.dilizy.dev",
        "user": "root",
        "hostname": "envtest.dilizy.dev",
        "registry": "registry.support.dev.dilizy.dev",
        "with_pwa": false,
        "mail_host": "smtp.gmail.com",
        "mail_port": "587",
        "subdomain": "envtest",
        "environment": "stage",
        "mail_mailer": "smtp",
        "private_key": "/home/<USER>/.ssh/ideil_wah_development",
        "company_name": "Test Company",
        "mail_password": "6GPQnQhnmA",
        "mail_username": "<EMAIL>",
        "registry_user": "registry_dilizy",
        "mail_encryption": "tls",
        "mail_from_address": "<EMAIL>",
        "registry_password": "gou4vaifophaiYie",
        "elasticsearch_host": "http://elasticsearch.service.dev.dilizy.dev:9200",
        "compose_project_name": "dms",
        "register_admin_email": "<EMAIL>"
    },
    "dms_passwords_and_keys": {
        "title": "DMS keys",
        "app_key": "base64:NpwN4zBHS88pNF3/510Piaxrxetfa1kvfrJRkKmRS7s=",
        "key_names": {
            "app_key": {
                "type": "laravel_app_key"
            },
            "pusher_app_id": {
                "type": "random_bin2hex",
                "length": 16
            },
            "pusher_app_key": {
                "type": "random_bin2hex",
                "length": 16
            },
            "redis_password": {
                "type": "password",
                "length": 14,
                "include": [
                    "letters",
                    "numbers",
                    "symbols"
                ]
            },
            "pusher_app_secret": {
                "type": "random_bin2hex",
                "length": 16
            }
        },
        "pusher_app_id": "5bbc0f7ba1fdc4dc",
        "pusher_app_key": "e5044600fa198500",
        "redis_password": "T7WGT|!Xt7,YzA",
        "pusher_app_secret": "7d1b94df1817ea70"
    }
}
EOD
            , true);

        $ids = [$contextData['dms']['elasticsearch_server_id']];
        $firewallId = $contextData['dms']['firewall_id'];

        $firewallsApi = new Firewalls();
        //$firewallsApi->removeFirewallFromServersById($firewallId, $ids);
        $firewallsApi->deleteFirewallById($firewallId);
    }

    private function startInstall()
    {
        $startInstall = new StartInstall();
        $startInstall->provideType(InstallType::BASIC_DMS_SETUP);
        $startInstall->providePayload(new InstallPayload([
            'subdomain' => 'envtest',
            'email' => '<EMAIL>',
            'company_name' => 'Test Company',
            'with_pwa' => false,
        ], null));
        $result = $startInstall->execute();

        $runInstall = new RunInstall($result->install);
        $runInstall->execute();
    }

    private function createImplementation(Stage $stage, Context $context): StageImplementation
    {
        /** @var \App\System\Installs\Stages\Contracts\StageImplementation */
        $implementation = app($stage->implementation);

        $implementation->linkWithStage($stage);
        if ($stage->scope) {
            $implementation->provideScope($stage->scope);
        }

        $implementation->provideContext($context);
        $implementation->provideName($stage->name);

        return $implementation;
    }

    private function runStage(int $id)
    {
        $stage = Stage::find($id);
        $previousStage = Stage::find($id - 1);
        if ($previousStage->install_id === $stage->install_id) {
            $stage->prior_context = $previousStage->resulting_context;
        }

        $context = new Context($stage->prior_context);
        $implementation = $this->createImplementation($stage, $context);
        try {
            $this->info('running stage "' . $stage->name . '"...');
            $implementation->run();
            $stage->resulting_context = $context->toArray();
            $stage->save();

            dump($context->get($stage->scope->main));
        } catch (Throwable $e) {
            $this->error($e->getMessage());

            $this->info('rolling back...');
            $implementation->rollback();

            throw $e;
        }
    }
}
