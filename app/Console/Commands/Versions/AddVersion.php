<?php

namespace App\Console\Commands\Versions;

use App\System\Apps\Data\AppType;
use App\System\Versions\Actions\AddVersion as AddVersionAction;
use App\System\Versions\Actions\Data\OrderingStrategy;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AddVersion extends Command
{
    protected $signature = 'app:versions:add {app_type} {tag} {name} {description?} {order?}';
    protected $description = 'Add new version';

    public function handle()
    {
        $validator = Validator::make($this->arguments(), [
            'app_type' => ['required', Rule::enum(AppType::class)],
            'order' => ['nullable', 'numeric'],
        ]);

        if ($validator->fails()) {
            $this->error("Validation error(s)!");
            $this->error($validator->errors()->toJson());

            return 1;
        }

        $addVersion = new AddVersionAction();

        $appType = AppType::from($this->argument('app_type'));
        $addVersion->provideAppType($appType);

        $tag = $this->argument('tag');
        $addVersion->provideTag($tag);

        $name = $this->argument('name');
        $description = $this->argument('description');
        $addVersion->provideTexts($name, $description);

        $order = $this->argument('order');
        if (! empty($order)) {
            $addVersion->provideOrderingStrategy(OrderingStrategy::MANUAL, $order);
        } else {
            $addVersion->provideOrderingStrategy(OrderingStrategy::AUTO_INCREMENT);
        }

        $version = $addVersion->execute();

        $this->info(sprintf('Successfully added new version `%s` with ID %d', $version->name, $version->id));
    }
}
