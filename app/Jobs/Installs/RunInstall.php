<?php

namespace App\Jobs\Installs;

use App\Models\Installs\Install;
use App\System\Installs\Actions\RunInstall as RunInstallAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RunInstall implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 1;

    public function __construct(
        #[WithoutRelations]
        public Install $install,
    ) {
        //
    }

    public function handle(): void
    {
        $this->install->refresh();

        $action = new RunInstallAction($this->install);
        $action->execute();
    }

    public function uniqueId(): string
    {
        return $this->install->id;
    }
}
