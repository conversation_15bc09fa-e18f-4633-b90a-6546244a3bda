<?php

namespace App\Support;

use Closure;
use InvalidArgumentException;
use Throwable;

class Perform
{
    private int $cooldown = 0;
    private Closure|null $onSuccess = null;
    private Closure|null $onFailure = null;
    private bool $autoThrowException = false;

    public function __construct(private Closure $task)
    {
        //
    }

    public static function task(Closure $task): static
    {
        return new static($task);
    }

    public function retryEvery(int $seconds): static
    {
        $this->cooldown = $seconds;

        return $this;
    }

    public function onSuccess(Closure|null $onSuccess = null): static
    {
        $this->onSuccess = $onSuccess;

        return $this;
    }

    public function onFailure(Closure|null $onFailure = null, bool $autoThrowException = false): static
    {
        $this->onFailure = $onFailure;
        $this->autoThrowException = $autoThrowException;

        return $this;
    }

    public function forAtLeast(int $times): void
    {
        if ($times < 0) {
            throw new InvalidArgumentException('Negative amount of retries makes no sense!');
        }

        $tries = 0;
        $lastError = null;
        while ($tries < $times) {
            try {
                $result = ($this->task)($tries);

                if ($result !== false) {
                    if ($this->onSuccess) {
                        ($this->onSuccess)($result);
                    }

                    return;
                }

                $tries++;
            } catch (Throwable $e) {
                $lastError = $e;
                $tries++;
            }

            if ($this->cooldown) {
                sleep($this->cooldown);
            }
        }

        if ($this->onFailure) {
            ($this->onFailure)($lastError);

            if ($this->autoThrowException && ! empty($lastError)) {
                throw $lastError;
            }
        } else {
            if (! empty($lastError)) {
                throw $lastError;
            }
        }
    }

    public function once(Closure|null $failure = null): void
    {
        $this->forAtLeast(1, $failure);
    }
}
