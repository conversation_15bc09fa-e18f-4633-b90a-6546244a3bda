<?php

namespace App\Http\Middleware;

use App\Models\Logs\ApiRequestLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class LogApiRequests
{
    private ApiRequestLog $record;

    public function __construct()
    {
        $this->record = new ApiRequestLog();
    }

    public function handle(Request $request, Closure $next): Response
    {
        $this->saveRequest($request);

        try {
            $response = $next($request);
            $this->saveResponse($response, $request);

            // @codeCoverageIgnoreStart
        } catch (Throwable $error) {
            /**
             * NOTE
             * Exceptions in $next($request) are going to be handled by <PERSON><PERSON>
             * out of the box and always converted into response, so technically
             * nothing will ever fall through here unless $this->saveResponse(...)
             * itself has some bugs.
             */

            $this->saveErrorResponse($error, $request);
        }
        // @codeCoverageIgnoreEnd

        return $response;
    }

    private function saveRequest(Request $request): void
    {
        $this->record->fill([
            'request_method' => $request->method(),
            'request_headers' => $request->headers->all(),
            'request_body' => json_decode($request->getContent(), true),
            'request_url' => $request->fullUrl(),
            'request_ip' => $request->ip(),
        ]);
        $this->record->tryAssigningModelFromApiGuard($request);
        $this->record->save();
    }

    // @codeCoverageIgnoreStart
    private function saveErrorResponse(Throwable $error, Request $request): void
    {
        $this->record->fill([
            'response_body' => config('app.debug') ? [
                'code' => $error->getCode(),
                'file' => $error->getFile(),
                'line' => $error->getLine(),
                'message' => $error->getMessage(),
            ] : null,
            'response_http_code' => 500,
        ]);
        $this->record->save();
    }
    // @codeCoverageIgnoreEnd

    private function saveResponse(Response $response, Request $request): void
    {
        $this->record->fill([
            'response_body' => json_decode($response->getContent(), true),
            'response_headers' => $response->headers->all(),
            'response_http_code' => $response->getStatusCode(),
        ]);
        $this->record->save();
    }
}
