<?php

namespace App\Http\Resources\Api\V1\Instances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InstanceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'group_id' => null,

            'type' => $this->resource->type,
            'status' => $this->resource->status,
            'result' => $this->resource->result,

            'stages' => $this->whenLoaded('stages', fn ($stages) => StageResource::collection($stages)),

            'ended_at' => $this->resource->ended_at?->toAtomString(),
            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
