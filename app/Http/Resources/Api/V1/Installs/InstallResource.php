<?php

namespace App\Http\Resources\Api\V1\Installs;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="InstallResource",
 *     title="InstallResource",
 *     description="Install object.",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Install ID.",
 *         example=23,
 *     ),
 *     @OA\Property(
 *         property="group_id",
 *         type="integer",
 *         description="Group ID this install belongs to.",
 *         example=3,
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"basic_dms_setup", "pwa_for_dms_setup"},
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         enum={"pending", "running", "finished", "failed", "canceled"},
 *         default="pending",
 *     ),
 *     @OA\Property(
 *         property="result",
 *         type="object",
 *         description="Additional object with install results once it is in the finished state.",
 *         oneOf={
 *             @OA\Schema(ref="#/components/schemas/BasicDmsSetupResultResource"),
 *             @OA\Schema(ref="#/components/schemas/PwaForDmsSetupResultResource"),
 *         },
 *     ),
 *     @OA\Property(
 *         property="stages",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/StageResource"),
 *     ),
 *     @OA\Property(
 *         property="ended_at",
 *         type="string",
 *         format="date-time",
 *         description="Date of ending in one of finished/failed/canceled statuses.",
 *         example="2023-02-06T12:00:00+00:00",
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Install creation date.",
 *         example="2023-02-06T10:29:11+00:00",
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Install last update date.",
 *         example="2023-02-06T10:29:11+00:00",
 *     ),
 * )
 */
class InstallResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'group_id' => null,

            'type' => $this->resource->type,
            'status' => $this->resource->status,
            'result' => $this->resource->result,

            'stages' => $this->whenLoaded('stages', fn ($stages) => StageResource::collection($stages)),

            'ended_at' => $this->resource->ended_at?->toAtomString(),
            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
