<?php

namespace App\Http\Resources\Api\V1\Installs;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="StageResource",
 *     title="StageResource",
 *     description="Stage object.",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Stage ID.",
 *         example="23"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         enum={"pending", "running", "finished", "failed", "canceled"},
 *         default="pending"
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"run", "rollback"},
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         example="Virtual machine setup",
 *     ),
 *     @OA\Property(
 *         property="logs",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/StageLogResource")
 *     ),
 *     @OA\Property(
 *         property="order",
 *         type="integer",
 *         default="0",
 *     ),
 *     @OA\Property(
 *         property="ended_at",
 *         type="string",
 *         format="date-time",
 *         description="Date of ending in one of finished/failed/canceled statuses.",
 *         example="2023-02-06T12:00:00+00:00"
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Stage creation date.",
 *         example="2023-02-06T10:29:11+00:00"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Stage last update date.",
 *         example="2023-02-06T10:29:11+00:00"
 *     ),
 * )
 */
class StageResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'status' => $this->resource->status,
            'type' => $this->resource->implementation_method,

            'name' => $this->resource->name,

            'logs' => $this->whenLoaded('logs', fn ($logs) => StageLogResource::collection($logs)),
            'order' => $this->resource->order,

            'ended_at' => $this->resource->ended_at?->toAtomString(),
            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
