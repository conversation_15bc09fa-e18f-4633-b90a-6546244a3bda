<?php

namespace App\Http\Resources\Api\V1\Installs;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="StageLogResource",
 *     title="StageLogResource",
 *     description="Stage log record.",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Stage log ID.",
 *         example="23"
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"info", "warning", "error"},
 *     ),
 *     @OA\Property(
 *         property="content",
 *         type="string",
 *         example="Sending request to create server via Hetzner Cloud API...",
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Stage log creation date.",
 *         example="2023-02-06T10:29:11+00:00"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Stage log last update date.",
 *         example="2023-02-06T10:29:11+00:00"
 *     ),
 * )
 */
class StageLogResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'type' => $this->resource->type,
            'content' => $this->resource->content,

            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
