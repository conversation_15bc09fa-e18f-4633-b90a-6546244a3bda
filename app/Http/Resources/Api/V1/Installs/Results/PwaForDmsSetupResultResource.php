<?php

namespace App\Http\Resources\Api\V1\Installs\Results;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="PwaForDmsSetupDataResource",
 *     title="PwaForDmsSetupDataResource",
 *     description="Input data object for `pwa_for_dms_setup` install type.",
 *     @OA\Property(
 *         property="dms_id",
 *         type="integer",
 *         description="DMS instance ID to enable PWA for.",
 *         example=14,
 *     ),
 * )
 *
 * @OA\Schema(
 *     schema="PwaForDmsSetupResultResource",
 *     title="PwaForDmsSetupResultResource",
 *     description="Result object for `pwa_for_dms_setup` install type.",
 *     @OA\Property(
 *         property="pwa",
 *         type="object",
 *         description="Created PWA data.",
 *         @OA\Property(
 *             property="id",
 *             type="integer",
 *             description="PWA instance ID.",
 *             example=63,
 *         ),
 *         @OA\Property(
 *             property="url",
 *             type="string",
 *             description="PWA full url.",
 *             example="https://foobar-trading.pwa.dilizy.dev",
 *         ),
 *         @OA\Property(
 *             property="name",
 *             type="string",
 *             description="PWA name.",
 *             example="foobar-trading",
 *         ),
 *     ),
 * )
 */
class PwaForDmsSetupResultResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'pwa' => [
                //
            ],
        ];
    }
}
