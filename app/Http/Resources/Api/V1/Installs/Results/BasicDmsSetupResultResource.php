<?php

namespace App\Http\Resources\Api\V1\Installs\Results;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="BasicDmsSetupDataResource",
 *     title="BasicDmsSetupDataResource",
 *     description="Input data object for `basic_dms_setup` install type.",
 *     @OA\Property(
 *         property="subdomain",
 *         type="string",
 *         description="Subdomain for new DMS instance.",
 *         example="foobar-trading",
 *     ),
 *     @OA\Property(
 *         property="email",
 *         type="string",
 *         format="email",
 *         description="Email to register DMS root user to.",
 *         example="<EMAIL>",
 *     ),
 *     @OA\Property(
 *         property="company_name",
 *         type="string",
 *         description="Company name.",
 *         example="Foobar Trading Inc.",
 *     ),
 *     @OA\Property(
 *         property="with_pwa",
 *         type="boolean",
 *         description="Whether or not to instruct DMS to do additional steps for PWA support.",
 *     ),
 * )
 *
 * @OA\Schema(
 *     schema="BasicDmsSetupResultResource",
 *     title="BasicDmsSetupResultResource",
 *     description="Result object for `basic_dms_setup` install type.",
 *     @OA\Property(
 *         property="dms",
 *         type="object",
 *         description="Created DMS data.",
 *         @OA\Property(
 *             property="id",
 *             type="integer",
 *             description="DMS instance ID.",
 *             example=7,
 *         ),
 *         @OA\Property(
 *             property="url",
 *             type="string",
 *             description="DMS full url.",
 *             example="https://foobar-trading.dilizy.dev",
 *         ),
 *         @OA\Property(
 *             property="name",
 *             type="string",
 *             description="DMS name.",
 *             example="foobar-trading",
 *         ),
 *         @OA\Property(
 *             property="credentials",
 *             type="object",
 *             description="Created user credentials for DMS system.",
 *             @OA\Property(
 *                 property="login",
 *                 type="string",
 *                 description="User login to access DMS system.",
 *                 example="foobar",
 *             ),
 *             @OA\Property(
 *                 property="email",
 *                 type="string",
 *                 format="email",
 *                 description="User email in DMS system.",
 *                 example="<EMAIL>",
 *             ),
 *         ),
 *         @OA\Property(
 *             property="api",
 *             type="object",
 *             description="Data for DMS API.",
 *             @OA\Property(
 *                 property="client_id",
 *                 type="integer",
 *                 description="Grant Client ID for OAuth2.",
 *                 example=2,
 *             ),
 *             @OA\Property(
 *                 property="client_secret",
 *                 type="string",
 *                 description="Grant Client Secret for OAuth2.",
 *                 example="sdf5423h1hcb7xb872h45sdfz2ds349",
 *             ),
 *         ),
 *     ),
 * )
 */
class BasicDmsSetupResultResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'dms' => [
                //
            ],
        ];
    }
}
