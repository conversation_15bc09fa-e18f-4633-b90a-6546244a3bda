<?php

namespace App\Http\Resources\Api\V1\Versions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VersionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'app_type' => $this->resource->app_type,
            'tag' => $this->resource->tag,
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'docker_compose_yaml' => $this->resource->docker_compose_yaml,
            'docker_compose_env' => $this->resource->docker_compose_env,

            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
