<?php

namespace App\Http\Resources\Api\V1\Info;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReleaseResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'type' => $this->resource->app_type->value,
            'hash' => $this->resource->tag,
            'title' => $this->resource->name,
            'description' => $this->resource->description,
            'changelog' => null,

            'created_at' => $this->resource->created_at->toAtomString(),
            'updated_at' => $this->resource->updated_at->toAtomString(),
        ];
    }
}
