<?php

namespace App\Http\Resources\Admin\Installs;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InstallResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,

            'type' => $this->resource->type,
            'payload' => $this->resource->payload,
            'status' => $this->resource->status,

            'created_at' => $this->resource->created_at->format('Y-m-d H:i:s'),
            'ended_at' => $this->resource->ended_at?->format('Y-m-d H:i:s'),
        ];
    }
}
