<?php

namespace App\Http\Controllers\Api\OAuth;

use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController as PassportAccessTokenController;
use Psr\Http\Message\ServerRequestInterface;

class AccessTokenController extends PassportAccessTokenController
{
    /**
     * @OA\Post(
     *     path="/oauth/token",
     *     summary="IssueToken",
     *     tags={"OAuth"},
     *
     *     @OA\RequestBody(
     *         required=true,
     *         description="Credentials",
     *
     *         @OA\JsonContent(
     *             required={"grant_type","client_id","client_secret"},
     *
     *             @OA\Property(
     *                 property="grant_type",
     *                 type="string",
     *                 example="client_credentials"
     *             ),
     *             @OA\Property(
     *                 property="client_id",
     *                 type="integer",
     *                 example=3
     *             ),
     *             @OA\Property(
     *                 property="client_secret",
     *                 type="string",
     *                 example="QNREYiv3H5tJMePQhRwJyPF3DaeyAaPTCPNfLzdc"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                  property="token_type",
     *                  type="string",
     *                  example="Bearer"
     *             ),
     *             @OA\Property(
     *                  property="expires_in",
     *                  type="integer",
     *                  example=31622400
     *             ),
     *             @OA\Property(
     *                 property="access_token",
     *                 type="string",
     *                 example="*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
     *             ),
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="error",
     *                 type="string",
     *                 example="invalid_request"
     *             ),
     *             @OA\Property(
     *                 property="error_description",
     *                 type="string",
     *                 example="The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed.",
     *             ),
     *             @OA\Property(
     *                 property="hint",
     *                 type="string",
     *                 example="Check the `client_id` parameter",
     *             ),
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed.",
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="error",
     *                  type="string",
     *                  example="invalid_request"
     *              ),
     *              @OA\Property(
     *                  property="error_description",
     *                  type="string",
     *                  example="Client authentication failed",
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string",
     *                  example="Client authentication failed",
     *              )
     *          )
     *      )
     * )
     */
    public function issueToken(ServerRequestInterface $request)
    {
        return parent::issueToken($request);
    }
}
