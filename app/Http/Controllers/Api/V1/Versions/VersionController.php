<?php

namespace App\Http\Controllers\Api\V1\Versions;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\Api\V1\Versions\VersionController\AddRequest;
use App\Http\Resources\Api\V1\Versions\VersionResource;
use App\System\Versions\Actions\AddVersion;

class VersionController extends ApiController
{
    public function add(AddRequest $request): VersionResource
    {
        /** @var \App\System\Versions\Actions\AddVersion */
        $addVersion = app(AddVersion::class);

        $addVersion
            ->provideAppType($request->getAppType())
            ->provideTag($request->tag)
            ->provideTexts($request->name, $request->description);

        if (! empty($request->docker_compose_yaml) && ! empty($request->docker_compose_env)) {
            $addVersion->provideDockerComposeConfigs(
                $request->docker_compose_yaml,
                $request->docker_compose_env,
                base64: true,
            );
        }

        $version = $addVersion->execute();

        return VersionResource::make($version);
    }
}
