<?php

namespace App\Http\Controllers\Api\V1\Installs;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\Api\V1\Installs\InstallController\ListRequest;
use App\Http\Requests\Api\V1\Installs\InstallController\StartRequest;
use App\Http\Requests\Api\V1\Installs\InstallController\ViewRequest;
use App\Http\Resources\Api\V1\Installs\InstallResource;
use App\Jobs\Installs\RunInstall;
use App\Models\Installs\Install;
use App\Models\Installs\Queries\InstallQueryFactory;
use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\ValidationException;

class InstallController extends ApiController
{
    /**
     * @OA\Get(
     *     path="/v1/install",
     *     summary="Get installs",
     *     description="Returns list of all installs.",
     *     operationId="getInstalls",
     *     tags={"Install"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         required=false,
     *         description="Show only installs in provided status.",
     *         @OA\Schema(
     *             type="string",
     *             enum={"pending", "running", "finished", "failed", "canceled"},
     *             default="pending"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of advertisements.",
     *         @OA\JsonContent(
     *             type="object",
     *             allOf={
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="data",
     *                         type="array",
     *
     *                         @OA\Items(ref="#/components/schemas/InstallResource")
     *                     )
     *                 ),
     *                 @OA\Schema(ref="#/components/schemas/PaginatedLinksResponse"),
     *                 @OA\Schema(ref="#/components/schemas/PaginatedMetaResponse"),
     *             },
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated.",
     *         @OA\JsonContent(ref="#/components/schemas/401Unauthorized")
     *     ),
     * )
     */
    public function list(ListRequest $request): AnonymousResourceCollection
    {
        /** @var \App\Models\Installs\Queries\InstallQueryFactory */
        $factory = app(InstallQueryFactory::class);
        $query = $factory->createFromListRequest($request);

        return InstallResource::collection($query->cursorPaginate());
    }

    /**
     * @OA\Post(
     *     path="/v1/install",
     *     summary="Start new install",
     *     description="Creates new install instance and start the procedure.",
     *     operationId="startInstall",
     *     tags={"Install"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"grant_type","client_id","client_secret"},
     *
     *             @OA\Property(
     *                 property="type",
     *                 type="string",
     *                 enum={"basic_dms_setup", "pwa_for_dms_setup"},
     *             ),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="Additional data that varies depending on provided type.",
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/BasicDmsSetupDataResource"),
     *                     @OA\Schema(ref="#/components/schemas/PwaForDmsSetupDataResource"),
     *                 },
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Created install.",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 ref="#/components/schemas/InstallResource",
     *             )
     *         ),
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated.",
     *         @OA\JsonContent(ref="#/components/schemas/401Unauthorized")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/422UnprocessableContent")
     *     ),
     * )
     */
    public function start(StartRequest $request): InstallResource
    {
        $data = $request->getData();

        // Mapping SCRM named version info (release_id) into deployer one (target_version) if present.
        $targetVersion = $request->getVersion();
        if ($targetVersion) {
            unset($data['release_id']);
            $data['target_version_id'] = $targetVersion->id;
        }

        $payload = new InstallPayload($data, $request->getGroup());

        try {
            $result = (new StartInstall())
                ->providePayload($payload)
                ->provideType($request->getType())
                ->execute();

            RunInstall::dispatch($result->install);
        } catch (ValidationException $e) {
            // We are prepending all keys with 'data.' because installation payload is taken from form request's
            // 'data' key.
            $prepended = collect($e->validator->getMessageBag()->getMessages())->mapWithKeys(function ($item, $key) {
                return ['data.' . $key => $item];
            })->all();

            throw ValidationException::withMessages($prepended);
        }

        return InstallResource::make($result->install->load('stages.logs'));
    }

    /**
     * @OA\Get(
     *     path="/v1/install/{id}",
     *     summary="Get a single install by id.",
     *     tags={"Install"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Install ID.",
     *         required=true,
     *         @OA\Schema(
     *             type="integer",
     *             example="25"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Install found.",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 ref="#/components/schemas/InstallResource",
     *             )
     *         ),
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated.",
     *         @OA\JsonContent(ref="#/components/schemas/401Unauthorized")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Install not found.",
     *         @OA\JsonContent(ref="#/components/schemas/404NotFound")
     *     ),
     * )
     */
    public function view(ViewRequest $request, Install $install): InstallResource
    {
        return InstallResource::make($install->load('stages.logs'));
    }
}
