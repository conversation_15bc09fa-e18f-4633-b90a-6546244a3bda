<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

define('API_URL', env('APP_URL') . '/api');

/**
 * @OA\Info(
 *      version="1.0.0",
 *      title="Deployer API documentation. V1",
 *      @OA\License(
 *          name="Apache 2.0",
 *          url="https://www.apache.org/licenses/LICENSE-2.0.html"
 *      )
 * )
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer"
 * )
 * @OA\Server(
 *     description="OpenApi host",
 *     url=API_URL
 * )
 *
 * @OA\Schema(
 *     schema="PaginatedLinksResponse",
 *     title="Paginated Links Response",
 *     description="Cursor pagination can only be used to display `Next` and `Previous` links and does not support generating links with page numbers.",
 *     @OA\Property(
 *         property="links",
 *         type="object",
 *         @OA\Property(
 *             property="first",
 *             type="string",
 *             format="uri",
 *             example=null
 *         ),
 *         @OA\Property(
 *             property="last",
 *             type="string",
 *             format="uri",
 *             example=null
 *         ),
 *         @OA\Property(
 *             property="prev",
 *             type="string",
 *             format="uri",
 *         ),
 *         @OA\Property(
 *             property="next",
 *             type="string",
 *             format="uri",
 *         ),
 *     ),
 * )
 *
 * @OA\Schema(
 *     schema="PaginatedMetaResponse",
 *     title="Paginated Meta Response",
 *     description="The JSON from the paginator will include meta information such as per_page, next_cursor, prev_cursor, and more.",
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         @OA\Property(
 *             property="path",
 *             type="string",
 *         ),
 *         @OA\Property(
 *             property="per_page",
 *             type="integer",
 *             example=15
 *         ),
 *         @OA\Property(
 *             property="next_cursor",
 *             type="string",
 *             example="eyJpZCI6NCwiX3BvaW50c1RvTmV4dEl0ZW1zIjp0cnVlfQ"
 *         ),
 *         @OA\Property(
 *             property="prev_cursor",
 *             type="string",
 *             example="eyJpZCI6MywiX3BvaW50c1RvTmV4dEl0ZW1zIjpmYWxzZX0"
 *         ),
 *     ),
 * )
 *
 * @OA\Schema(
 *     schema="401Unauthorized",
 *     title="401 Unauthorized",
 *     description="You not have access rights to this action.",
 *     @OA\Property(property="message", type="string", example="Unauthenticated")
 * )
 * @OA\Schema(
 *     schema="403Forbidden",
 *     title="403 Forbidden",
 *     description="You must authenticate itself to get the requested response.",
 *     @OA\Property(property="message", type="string", example="This action is unauthorized")
 * )
 * @OA\Schema(
 *     schema="404NotFound",
 *     title="404 Not Found",
 *     description="The server cannot find the requested resource.",
 *     @OA\Property(property="message", type="string", example="No query results for model")
 * )
 * @OA\Schema(
 *     schema="422UnprocessableContent",
 *     title="422 Unprocessable Content",
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Validation message.",
 *         example="The title field is required. (and 1 more error)"
 *     ),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         description="Validation errors.",
 *         additionalProperties={
 *             "type": "array",
 *             "items": {"type": "string"}
 *         }
 *     )
 * )
 */
class ApiController extends Controller
{
    /**
     * @OA\Response(
     *     response=404,
     *     description="The requested URL was not found on this server.",
     *     @OA\JsonContent(
     *         @OA\Property(
     *             property="error",
     *             type="object",
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="The requested URL was not found on this server."
     *             ),
     *             @OA\Property(
     *                 property="details",
     *                 type="string",
     *                 example="The URL you requested does not exist or is not accessible. Please check the URL and try again."
     *             )
     *         )
     *     )
     * )
     */
    public function wrongUrl(): JsonResponse
    {
        return response()->json([
            'message' => 'The requested URL was not found on this server.',
            'details' => 'The URL you requested does not exist or is not accessible. Please check the URL and try again.',
        ], Response::HTTP_NOT_FOUND);
    }
}
