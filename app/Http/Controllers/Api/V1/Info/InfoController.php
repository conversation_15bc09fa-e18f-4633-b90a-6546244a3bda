<?php

namespace App\Http\Controllers\Api\V1\Info;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\Api\V1\Info\InfoController\ReleasesRequest;
use App\Http\Resources\Api\V1\Info\ReleaseResource;
use App\Models\Versions\Queries\VersionQueryFactory;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class InfoController extends ApiController
{
    public function releases(ReleasesRequest $request): AnonymousResourceCollection
    {
        /** @var \App\Models\Versions\Queries\VersionQueryFactory */
        $factory = app(VersionQueryFactory::class);
        $query = $factory->createFromReleasesRequest($request);

        return ReleaseResource::collection($query->get());
    }
}
