<?php

namespace App\Http\Controllers\Api\V1\Instances;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\Api\V1\Instances\InstanceController\DeleteRequest;
use App\Http\Requests\Api\V1\Instances\InstanceController\UpdateLimitsStats;
use App\Http\Requests\Api\V1\Instances\InstanceController\ViewRequest;
use App\Http\Requests\Api\V1\Instances\InstanceController\ReleaseViewRequest;
use App\Http\Requests\Api\V1\Instances\InstanceController\ReleaseStartRequest;
use App\Http\Resources\Api\V1\Installs\InstallResource;
use App\Http\Resources\Api\V1\Instances\InstanceResource;
use App\Jobs\Installs\RunInstall;
use App\Models\Infrastructure\Instance;
use App\Models\Installs\Install;
use App\System\Apps\Data\AppType;
use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallType;
use App\System\Installs\Data\RunType;
use Illuminate\Validation\ValidationException;
use LogicException;

class InstanceController extends ApiController
{
    public function view(ViewRequest $request, Instance $instance): InstanceResource
    {
        return InstanceResource::make($instance);
    }

    public function delete(DeleteRequest $request, Instance $instance): InstallResource
    {
        if ($instance->app_type === AppType::DMS) {
            $payload = new InstallPayload([
                'dms_id' => $instance->id,
            ], null);

            $result = (new StartInstall())
                ->providePayload($payload)
                ->provideRunType(RunType::IGNORE_ERRORS_NO_ROLLBACKS)
                ->provideType(InstallType::DELETE_DMS_INSTANCE)
                ->execute();
        } elseif ($instance->app_type === AppType::PWA) {
            abort(403, 'Deleting PWA instances is not allowed! Delete its DMS one.');
        } else {
            abort(403, 'Deleting instances of this type is not supported!');
        }

        RunInstall::dispatch($result->install);

        return InstallResource::make($result->install->load('stages.logs'));
    }

    public function releaseView(ReleaseViewRequest $request, Instance $instance, Install $install): InstallResource
    {
        return InstallResource::make($install->load('stages.logs'));
    }

    public function releaseStart(ReleaseStartRequest $request, Instance $instance, Install $install): InstallResource
    {
        $version = $request->getVersion();

        if ($instance->app_type === AppType::DMS) {
            $payload = [
                'dms_id' => $instance->id,
                'target_version_id' => $version->id,
            ];
            $type = InstallType::UPGRADE_DMS;
        } elseif ($instance->app_type === AppType::PWA) {
            $payload = [
                'pwa_id' => $instance->id,
                'target_version_id' => $version->id,
            ];
            $type = InstallType::UPGRADE_PWA;
        } else {
            throw new LogicException(sprintf(
                'Instance ID %d of type `%s` does not support upgrades!',
                $instance->id,
                $instance->app_type->value,
            ));
        }

        $payload = new InstallPayload($payload, $install->group);

        try {
            $result = (new StartInstall())
                ->providePayload($payload)
                ->provideType($type)
                ->execute();

            RunInstall::dispatch($result->install);
        } catch (ValidationException $e) {
            // We are prepending all keys with 'data.' because installation payload is taken from form request's
            // 'data' key.
            $prepended = collect($e->validator->getMessageBag()->getMessages())->mapWithKeys(function ($item, $key) {
                return ['data.' . $key => $item];
            })->all();

            throw ValidationException::withMessages($prepended);
        }

        return InstallResource::make($result->install->load('stages.logs'));
    }

    public function updateLimitsStats(UpdateLimitsStats $request)
    {
        $instances = $request->get('instances');

        $installs = collect();

        try {
            foreach ($instances as $instance) {
                $payload = new InstallPayload((array)$instance, null);

                $result = (new StartInstall())
                    ->providePayload($payload)
                    ->provideType(InstallType::UPGRADE_DMS_INSTANCES_LIMITS_STATS)
                    ->execute();

                RunInstall::dispatch($result->install);

                $installs->push($result->install->load('stages.logs'));
            }
        } catch (ValidationException $e) {
            // If your validation keys are flat (not nested under `data`), no need to prepend
            $messages = collect($e->validator->getMessageBag()->getMessages())->mapWithKeys(function ($item, $key) {
                return [$key => $item];
            })->all();

            throw ValidationException::withMessages($messages);
        }

        return InstallResource::collection($installs);
    }
}
