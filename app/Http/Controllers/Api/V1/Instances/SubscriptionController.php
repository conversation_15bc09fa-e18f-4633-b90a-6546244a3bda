<?php

namespace App\Http\Controllers\Api\V1\Instances;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\Api\V1\Instances\SubscriptionController\SubscriptionRequest;
use App\Http\Resources\Api\V1\Installs\InstallResource;
use App\Jobs\Installs\RunInstall;
use App\System\Installs\Actions\StartInstall;
use App\System\Installs\Data\InstallPayload;
use App\System\Installs\Data\InstallType;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\ValidationException;

class SubscriptionController extends ApiController
{
	public function updateSubscription(SubscriptionRequest $request): AnonymousResourceCollection
	{
		$instances = $request->get('instances');

		$installs = collect();

		try {
			foreach ($instances as $instance) {
				$payload = new InstallPayload((array)$instance, null);

				$result = (new StartInstall())
					->providePayload($payload)
					->provideType(InstallType::UPGRADE_DMS_SUBSCRIPTION)
					->execute();

				RunInstall::dispatch($result->install);

				$installs->push($result->install->load('stages.logs'));
			}
		} catch (ValidationException $e) {
			// If your validation keys are flat (not nested under `data`), no need to prepend
			$messages = collect($e->validator->getMessageBag()->getMessages())->mapWithKeys(function ($item, $key) {
				return [$key => $item];
			})->all();

			throw ValidationException::withMessages($messages);
		}

		return InstallResource::collection($installs);
	}
}
