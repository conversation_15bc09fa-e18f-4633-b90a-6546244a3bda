<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\Installs\InstallResource;
use App\Models\Installs\Install;
use Inertia\Inertia;

class InstallsController extends Controller
{
    public function view(Install $install)
    {
        return Inertia::render('Installs/View', [
            'install' => InstallResource::make($install)->resolve(),
        ]);
    }

    public function list()
    {
        return Inertia::render('Installs/List', []);
    }

    public function apiList()
    {
        $installs = Install::latest()->cursorPaginate();

        return InstallResource::collection($installs);
    }
}
