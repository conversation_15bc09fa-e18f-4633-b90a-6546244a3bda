<?php

namespace App\Http\Requests\Api\V1\Instances\InstanceController;

use App\Models\Versions\Version;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReleaseStartRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'release_id' => [
                'required',
                Rule::exists('versions', 'id')->withoutTrashed(),
            ],
        ];
    }

    public function getVersion(): Version
    {
        return Version::find($this->release_id);
    }
}
