<?php

namespace App\Http\Requests\Api\V1\Versions\VersionController;

use App\System\Apps\Data\AppType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'app_type' => ['required', Rule::enum(AppType::class)],
            'name' => ['required'],
            'tag' => ['required', Rule::unique('versions', 'tag')],
            'description' => ['nullable'],
            'docker_compose_yaml' => ['required_with:docker_compose_env'],
            'docker_compose_env' => ['required_with:docker_compose_yaml'],
        ];
    }

    public function getAppType(): AppType
    {
        return AppType::from($this->app_type);
    }
}
