<?php

namespace App\Http\Requests\Api\V1\Info\InfoController;

use App\System\Apps\Data\AppType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules\Enum;

class ReleasesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['nullable', new Enum(AppType::class)],
            'after' => ['nullable', 'integer'],
        ];
    }

    public function getType(): AppType|null
    {
        if (empty($this->type)) {
            return null;
        }

        return AppType::from($this->type);
    }

    public function getAfter(): Carbon|null
    {
        if (empty($this->after)) {
            return null;
        }

        return Carbon::createFromTimestamp($this->after);
    }
}
