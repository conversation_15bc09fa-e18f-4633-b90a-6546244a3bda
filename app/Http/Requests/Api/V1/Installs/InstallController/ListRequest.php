<?php

namespace App\Http\Requests\Api\V1\Installs\InstallController;

use App\System\Installs\Data\InstallStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'status' => ['nullable', new Enum(InstallStatus::class)],
        ];
    }

    public function getStatus(): InstallStatus|null
    {
        if (empty($this->status)) {
            return null;
        }

        return InstallStatus::from($this->status);
    }
}
