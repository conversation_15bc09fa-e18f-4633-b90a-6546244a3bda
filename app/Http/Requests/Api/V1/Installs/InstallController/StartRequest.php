<?php

namespace App\Http\Requests\Api\V1\Installs\InstallController;

use App\Models\Infrastructure\Group;
use App\Models\Versions\Version;
use App\System\Installs\Data\InstallType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StartRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'group_id' => [
                'nullable',
                Rule::exists('groups')->withoutTrashed()
            ],
            'type' => ['required', Rule::enum(InstallType::class)],
            'data' => ['nullable'],
        ];
    }

    public function getType(): InstallType
    {
        return InstallType::from($this->type);
    }

    public function getData(): array
    {
        if (empty($this->data)) {
            return [];
        }

        return (array) $this->data;
    }

    public function getGroup(): Group|null
    {
        return Group::find($this->group_id);
    }

    public function getVersion(): Version|null
    {
        if (empty($this->data['release_id'])) {
            return null;
        }

        return Version::find($this->data['release_id']);
    }
}
