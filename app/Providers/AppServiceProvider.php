<?php

namespace App\Providers;

use App\Support\Auth\ApiToken;
use App\System\Runtime\CommandHub;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Passport::ignoreRoutes();

        $this->app->instance('command.hub', new CommandHub());
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Auth::viaRequest('simple-api-token', function (Request $request) {
            $authHeader = $request->header('Authorization');
            if (empty($authHeader)) {
                return null;
            }

            if (! preg_match('/^Bearer (?<token>.+)$/', $authHeader, $matches)) {
                return null;
            }

            if ($matches['token'] !== env('API_TOKEN')) {
                return null;
            }

            return new ApiToken($matches['token']);
        });
    }
}
