# Deployer

## Requirements

- PHP 8.2+
- MySQL 8.0+
- Redis

## Installation and configuration

1.   Clone/copy project's source code.
2.   Run the following commands:

     ```bash
       composer install --no-dev --no-plugins --optimize-autoloader
       npm install
       npm run build
       cp .env.example .env
       php artisan key:generate --force
     ```

3.   Enter configuration values to `.env` file.
4.   Run the following commands:

     ```bash
       php artisan migrate --force
       php artisan db:seed --force
       php artisan passport:keys
       php artisan l5-swagger:generate --all
     ```

5.   Run `php artisan passport:client --client --name="SCRM Grant Client"` and provide resulting client ID and secret to the SCRM app.
6.   Ensure `php artisan horizon` is up and running.
7.   Add the following entry to your cron configuration: `* * * * * cd /path-to-this-project && php artisan schedule:run >> /dev/null 2>&1`.

## Environment Variables

Here is the list of Deployer app specific variables:
* `API_TOKEN`: token used for Bearer Authorization header to authorize some of the API requests (i.e., adding new app versions).

Here is the list of Hetzner specific env variables and their description:

* `HETZNER_CLOUD_API_TOKEN`: token to access Hetzner Cloud API;
* `HETZNER_CLOUD_API_URL`: fully qualified URL for Hetzner Cloud API (most likely will always be: https://api.hetzner.cloud);
* `HETZNER_CLOUD_NETWORK_NAMES`: Hetzner Cloud network name(s) to attach new server instance to (separated by commas if multiple, i.e., network1,network2,network3);
* `HETZNER_CLOUD_FIREWALL_NAMES`: Hetzner Cloud firewall name(s) to attach to the new server instance (separated by commas if multiple, i.e., fw1,fw2,fw3);
* `HETZNER_CLOUD_SSH_KEY_NAMES`: Hetzner Cloud SSH key name(s) to add to the new server instance (separated by commas if multiple, i.e., teamssh,devssh,client);
* `HETZNER_CLOUD_SSH_KEY_FILE`: path towards SSH key that Deployer will use to connect to this new server instance (must be listed in `HETZNER_CLOUD_SSH_KEY_NAMES` above), it is recommended to use absolute paths (i.e., /home/<USER>/.ssh/mykey);
* `HETZNER_CLOUD_SERVER_NAME_TEMPLATE`: template for name of the new server instance featuring a required `%s` parameter (i.e., `dilizy-dev-%s`);
* `HETZNER_CLOUD_SERVER_TYPE_DMS`: Hetzner server type to use for new instance;
* `HETZNER_DNS_API_TOKEN`: token to access Hetzner DNS API;
* `HETZNER_DNS_API_URL`: fully qualified URL for Hetzner DNS API (most likely will always be: https://dns.hetzner.com/api);

Here is the list of deployment specific env variables and their description:

* `DEPLOY_ENVIRONMENT`: environment name (i.e., production, stage etc.);
* `DEPLOY_ZBX_METADATA`: Zabbix metadata;
* `DEPLOY_DMS_ZONE_NAME`: domain to use for new DMS instances subdomains (i.e., dilizy.run).
* `DEPLOY_DMS_REGISTRY`: hostname of Docker registry for DMS (i.e., registry.support.dev.dilizy.dev);
* `DEPLOY_DMS_REGISTRY_USER`: Docker registry auth user;
* `DEPLOY_DMS_REGISTRY_PASSWORD`: Docker registry auth password;
* `DEPLOY_DMS_TAG`: Docker registry DMS tag to use for deployment by default (if version was not specified via initiating API call explicitly);
* `DEPLOY_DMS_PDF_BASE_URL`: fully qualified URL to PDF service (i.e., https://pdf.dev.dilizy.dev);
* `DEPLOY_DMS_GOOGLE_MAPS_API_KEY`: Google Maps API key to use for DMS map widgets;
* `DEPLOY_DMS_MAIL_MAILER`, `DEPLOY_DMS_MAIL_HOST`, `DEPLOY_DMS_MAIL_PORT`, `DEPLOY_DMS_MAIL_USERNAME`, `DEPLOY_DMS_MAIL_PASSWORD`, `DEPLOY_DMS_MAIL_ENCRYPTION`, `DEPLOY_DMS_MAIL_FROM_ADDRESS`: see Laravel documentation for configuring mail (variable names reflect those of Laravel framework for this matter);
* `DEPLOY_CHECKER_SSH_USER`: user to login under via SSH to the server containing checker service (SSH key specified in `HETZNER_CLOUD_SSH_KEY_FILE` will be used and thus must be authorized beforehand for this user);
* `DEPLOY_CHECKER_HOST`: host containing checker service (i.e., **************);
* `DEPLOY_CHECKER_URL`: fully qualified URL of this checker service (i.e., https://checker.additional.dev.dilizy.dev);
* `DEPLOY_PWA_ZONE_NAME`: domain to use for new PWA instances subdomains (i.e., dilizy.dev).
* `DEPLOY_PWA_URL`: fully qualified URL of admin PWA instance (i.e., https://admin.dilizy.dev);
* `DEPLOY_PWA_API_CLIENT_ID`: admin PWA instance OAuth client ID;
* `DEPLOY_PWA_API_CLIENT_SECRET`: admin PWA instance OAuth client secret;
* `DEPLOY_PWA_ADMIN_URL`: fully qualified URL of admin PWA instance to be specified for public PWA instance (i.e., https://admin.dilizy.dev);
* `DEPLOY_PWA_ADMIN_CLIENT_ID`: admin PWA instance OAuth client ID for public PWA instance to use;
* `DEPLOY_PWA_SECRET`: admin PWA instance OAuth client secret for public PWA instance to use;
* `DEPLOY_PWA_REGISTRY`: hostname of Docker registry for PWA (i.e., registry.support.dev.dilizy.dev);
* `DEPLOY_PWA_REGISTRY_USER`: Docker registry auth user;
* `DEPLOY_PWA_REGISTRY_PASSWORD`: Docker registry auth password;
* `DEPLOY_PWA_TAG`: Docker registry DMS tag to use for deployment;
* `DEPLOY_PWA_VAPID_PUBLIC_KEY`, `DEPLOY_PWA_VAPID_PRIVATE_KEY`: values for `VAPID_PUBLIC_KEY`/`VAPID_PRIVATE_KEY` variables in PWA environment;
* `DEPLOY_PWA_MAIL_MAILER`, `DEPLOY_PWA_MAIL_HOST`, `DEPLOY_PWA_MAIL_PORT`, `DEPLOY_PWA_MAIL_USERNAME`, `DEPLOY_PWA_MAIL_PASSWORD`, `DEPLOY_PWA_MAIL_ENCRYPTION`, `DEPLOY_PWA_MAIL_FROM_ADDRESS`: see Laravel documentation for configuring mail (variable names reflect those of Laravel framework for this matter).
* `DEPLOY_DMS_MARKETPLACES_ENCRYPTION_KEY`: Encryption key for marketplaces credentials

Elasticsearch setup:

* `DEPLOY_DMS_ELASTICSEARCH_HOST`: fully qualified URL to Elasticsearch service (specify port as well if it differs from the standard one, i.e., http://elasticsearch.internal.service.dev.dilizy.dev:9200);
* `DEPLOY_DMS_ELASTICSEARCH_ENABLE_AUTH`: if `false` then DMS environment will not be setup with `ELASTICSEARCH_USER` and `ELASTICSEARCH_PASSWORD` variables, otherwise (if `true`) additional variables below are required to be configured;
* `DEPLOY_DMS_ELASTICSEARCH_USER`: username to use for DMS Elasticsearch connection;
* `DEPLOY_DMS_ELASTICSEARCH_GENERATE_PASSWORD`: if `true` then `DEPLOY_DMS_ELASTICSEARCH_PASSWORD` variable value will be ignored and Deployer will generate random password, otherwise (if `false`) `DEPLOY_DMS_ELASTICSEARCH_PASSWORD` value will be used verbatim;
* `DEPLOY_DMS_ELASTICSEARCH_PASSWORD`: password to use for DMS Elasticsearch connection, its value is ignored if `DEPLOY_DMS_ELASTICSEARCH_GENERATE_PASSWORD` is set to `true`.

Sentry setup:

* `DEPLOY_DMS_SENTRY_ENVIRONMENT`: optional meta variable to specify environment name that will be attached to Sentry reports for DMS instance;
* `DEPLOY_DMS_SENTRY_RELEASE`: optional meta variable to specify release version info that will be attached to Sentry reports for DMS instance;
* `DEPLOY_DMS_SENTRY_DSN`: fully qualified URL of Sentry DSN to use for DMS instance (IMPORTANT! If this value is not empty it will take precedence over all `DEPLOY_SENTRY_*` variables listed below and deployer will skip Sentry project creation step for DMS);
* `DEPLOY_PWA_SENTRY_ENVIRONMENT`: optional meta variable to specify environment name that will be attached to Sentry reports for PWA instance;
* `DEPLOY_PWA_SENTRY_RELEASE`: optional meta variable to specify release version info that will be attached to Sentry reports for PWA instance;
* `DEPLOY_PWA_SENTRY_DSN`: fully qualified URL of Sentry DSN to use for PWA instance (IMPORTANT! If this value is not empty it will take precedence over all `DEPLOY_SENTRY_*` variables listed below and deployer will skip Sentry project creation step for PWA);
* `DEPLOY_SENTRY_URL`: fully qualified URL of Sentry service (i.e., https://sentry.monitoring.dilizy.dev);
* `DEPLOY_SENTRY_DMS_PROJECT_NAME_TEMPLATE`: template for name of the new DMS Sentry project featuring a required `%s` parameter (i.e., `dms-%s-dilizy-dev-hetzner`);
* `DEPLOY_SENTRY_PWA_PROJECT_NAME_TEMPLATE`: template for name of the new PWA Sentry project featuring a required `%s` parameter (i.e., `public-pwa-%s-dilizy-dev-hetzner`);
* `DEPLOY_SENTRY_ORGANIZATION_SLUG`: slug of a organization to create new Sentry projects to;
* `DEPLOY_SENTRY_TEAM_SLUG`: slug of a team to create new Sentry projects to;
* `DEPLOY_SENTRY_AUTH_TOKEN`: internal integration authentication token to use for Sentry API access.

CDN related setup:

* `DEPLOY_DMS_AWS_URL`: fully qualified URL of AWS s3-like storage service (IMPORTANT! If this value is not empty it will take precedence over all `DEPLOY_CDN_*` variables listed below and deployer will skip Minio bucket creation step for DMS);
* `DEPLOY_DMS_AWS_BUCKET`, `DEPLOY_DMS_AWS_ACCESS_KEY_ID` `DEPLOY_DMS_AWS_ACCESS_SECRET_KEY`: bucket data to use on this service;
* `DEPLOY_CDN_URL`: fully qualified URL of CDN service (i.e., https://cdn.service.dev.dilizy.dev);
* `DEPLOY_CDN_MINIO_HOST`: Minio host (i.e., cdn.service.dev.dilizy.dev);
* `DEPLOY_CDN_BASIC_AUTH_USER`: basic auth user;
* `DEPLOY_CDN_BASIC_AUTH_PASSWORD`: basic auth password;
* `DEPLOY_CDN_CICD_ACCESS_KEY`: CI/CD access key;
* `DEPLOY_CDN_CICD_SECRET_KEY`: CI/CD secret key.

## Testing

This project utilizes [PEST](https://pestphp.com) together with Laravel's out of the box PHPUnit/Mockery testing framework. Run `vendor/bin/pest` and make sure all the tests are passing successfully.

**Important!** Database-dependent integration/feature tests are refreshing database contents before each run, so create dedicated database for testing purposes only and configure `TESTING_DB_*` values in `.env` accordingly.

## Code Style

This project adheres to [PSR-12 coding style standard](https://www.php-fig.org/psr/psr-12) and uses [Laravel Pint](https://laravel.com/docs/11.x/pint) to keep it consistent. Run `vendor/bin/pint` to fix code styling.
