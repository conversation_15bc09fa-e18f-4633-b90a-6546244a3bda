x-aliases:
  - &common_parameters
    logging:
      driver: json-file
      options:
        max-file: '5'
        max-size: 20m
    restart: always
    networks:
      - dms_shared_network
    dns:
      - **************
      - **************
      - *******
      - *******

services:
  dmsphpfpm:
    image: ${REGISTRY}/dms/php:${TAG} # dev-docker-cd4c1a63
    hostname: ${VM_NAME}-dms
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - /opt/docker/dms/public:/var/www/html/dms/public:rw
      - /opt/docker/dms/storage:/var/www/html/dms/storage:rw
    environment:
      WEBEX24_WEBHOOK_SIGNATURE:
      STRUCTURE:
      APP_NAME:
      APP_ENV:
      APP_KEY:
      APP_DEBUG:
      APP_URL:

      LOG_CHANNEL:
      LOG_DEPRECATIONS_CHANNEL:
      LOG_LEVEL:

      DB_CONNECTION: "mysql"
      DB_HOST: "mysql"
      DB_PORT: "3306"
      DB_DATABASE:
      DB_USERNAME:
      DB_PASSWORD:

      BROADCAST_DRIVER:
      CACHE_DRIVER:
      FILESYSTEM_DISK:
      QUEUE_CONNECTION:
      SESSION_DRIVER:
      SESSION_LIFETIME:

      MEMCACHED_HOST:

      REDIS_HOST: "dms-redis-1"
      REDIS_PASSWORD:
      REDIS_PORT: "6379"

      MAIL_MAILER:
      MAIL_HOST:
      MAIL_PORT:
      MAIL_USERNAME:
      MAIL_PASSWORD:
      MAIL_ENCRYPTION:
      MAIL_FROM_ADDRESS:
      MAIL_FROM_NAME:

      AWS_ACCESS_KEY_ID:
      AWS_SECRET_ACCESS_KEY:
      AWS_DEFAULT_REGION:
      AWS_BUCKET:
      AWS_USE_PATH_STYLE_ENDPOINT:
      AWS_URL:

      PUSHER_APP_KEY:
      PUSHER_APP_SECRET:
      PUSHER_APP_ID:
      PUSHER_APP_HOST:
      PUSHER_APP_PORT:
      PUSHER_APP_CLUSTER:
      PUSHER_APP_SCHEME:

      VITE_PUSHER_APP_KEY:
      VITE_PUSHER_HOST:
      VITE_PUSHER_PORT:
      VITE_PUSHER_SCHEME:
      VITE_PUSHER_APP_CLUSTER:

      SENTRY_LARAVEL_DSN:
      SENTRY_TRACES_SAMPLE_RATE:
      SENTRY_RELEASE:
      SENTRY_ENVIRONMENT:

      MEDIA_DISK:

      GOOGLE_MAP_API_KEY:

      QUERY_DETECTOR_ENABLED:
      DEBUGBAR_ENABLED:

      CHECKER_TOKEN:
      CHECKER_BASE_URL:
      CHECKER_CLIENT_ID:
      CHECKER_CLIENT_SECRET:

      AI_SERVICE_BASE_URL:
      AI_SERVICE_TOKEN:

      SCRM_BASE_URL:
      SCRM_CLIENT_ID:
      SCRM_CLIENT_SECRET:
      SCRM_GRANT_TYPE:
      SCRM_SCOPE:

      ENVELOPE_BASE_URL:
      ENVELOPE_APP_ID:
      ENVELOPE_AUTH_KEY_ID:
      ENVELOPE_AUTH_KEY_SECRET:

      MARKETPLACES_ENCRYPTION_KEY:

      ELASTICSEARCH_HOST:
      ELASTICSEARCH_USER:
      ELASTICSEARCH_PASSWORD:

      EMAIL:
      COMPANY_NAME:
      HAS_PWA:

      PDF_BASE_URL:

      BIG_DATA_BASE_URL:
      BIG_DATA_CLIENT_ID:
      BIG_DATA_CLIENT_SECRET:
    <<: *common_parameters

  nginx:
    image: ${REGISTRY}/dms/nginx:${TAG} # dev-docker-cd4c1a63
    depends_on:
      - dmsphpfpm
    ports:
      - "80:80"
      - "443:443"
    environment:
      DMS_HOST:
      DMS_DOMAIN:
      DMS_HOSTNAME: "${DMS_HOST}.${DMS_DOMAIN}"
      PHP_HOST_DMS: "dms-dmsphpfpm-1"
      HAS_PWA:
      PWA_HOST:
      PWA_DOMAIN:
      PWA_HOSTNAME: "${PWA_HOST}.${PWA_DOMAIN}"
      PHP_HOST_PWA: "pwa-pwaphpfpm-1"
    volumes:
      - /opt/docker/dms/public:/var/www/html/dms/public:ro
      - /opt/docker/dms/storage:/var/www/html/dms/storage:ro
      - /opt/docker/pwa/public:/var/www/html/pwa/public:ro
      - /opt/docker/pwa/storage:/var/www/html/pwa/storage:ro
      - certs:/etc/nginx/certs
    <<: *common_parameters

  mysql:
    image: ${REGISTRY}/dms/mysql:${TAG}
    cap_add:
      - SYS_NICE
    environment:
      MYSQL_ROOT_PASSWORD:
      MYSQL_DATABASE: $DB_DATABASE
      MYSQL_USER: $DB_USERNAME
      MYSQL_PASSWORD: $DB_PASSWORD
      PERCONA_TELEMETRY_DISABLE: "1"
    volumes:
      - database:/var/lib/mysql
    <<: *common_parameters

  redis:
    image: ${REGISTRY}/dms/redis:${TAG}
    environment:
      REDIS_PASSWORD:
    <<: *common_parameters

  zabbix:
    image: zabbix/zabbix-agent2:${ZBX_TAG}
    group_add:
      - "999"
    environment:
      ZBX_SERVER_HOST:
      ZBX_PASSIVE_ALLOW: "true"
      ZBX_ACTIVE_ALLOW: "true"
      ZBX_TIMEOUT: "30"
      ZBX_HOSTNAME: "${DMS_HOST}.${DMS_DOMAIN}"
      ZBX_DEBUGLEVEL: "3"
      ZBX_METADATA:
    ports:
      - "10050:10050"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /var/run/docker.sock:/var/run/docker.sock
    privileged: true
    pid: "host"
    <<: *common_parameters

  loki:
    image: ${REGISTRY}/dms/loki:${TAG}
    volumes:
      - /var/log:/var/log
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      VM_NAME:
      LOKI_URL:
    <<: *common_parameters

  bareos-fd:
    image: ${REGISTRY}/dms/bareos:${TAG}
    ports:
      - "9102:9102"
    volumes:
      - bareos-conf:/etc/bareos
      - bareos-restores:/tmp/bareos-restores
      - /opt/docker/:/opt/docker/
    environment:
      BAREOS_FD_PASSWORD:
      BAREOS_DIRECTOR_IP:
      BAREOS_CLIENT_NAME:
    restart: always
    <<: *common_parameters
volumes:
  certs:
  database:
  bareos-conf:
  bareos-restores:

networks:
  dms_shared_network:
    external: true
