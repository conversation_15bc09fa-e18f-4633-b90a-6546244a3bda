COMPOSE_PROJECT_NAME="dms"

REGISTRY="registry.support.dilizy.dev"
TAG="v0-6-4-f4face69"

ZBX_TAG=alpine-6.0-latest
ZBX_SERVER_HOST=zabbix.monitoring.monitoring.dilizy.dev
ZBX_METADATA=
LOKI_URL=https://loki.monitoring.monitoring.dilizy.dev/loki/api/v1/push
VM_NAME=westautohubb.dilizy.dev


DMS_HOST="dms"
DMS_DOMAIN="dilizy.run"
PWA_HOST="dms"
PWA_DOMAIN="dilizy.dev"

MYSQL_ROOT_PASSWORD=root_PWD_pass
#################### DMS ENV ########################
WEBEX24_WEBHOOK_SIGNATURE=
STRUCTURE=

APP_NAME=
APP_ENV=
APP_KEY=_some_key_
APP_DEBUG=
APP_URL=

LOG_CHANNEL="stack"
LOG_DEPRECATIONS_CHANNEL="null"
LOG_LEVEL="debug"

DB_CONNECTION="mysql"
DB_HOST="dms-mysql-1"
DB_PORT="3306"
DB_DATABASE=db
DB_USERNAME=myser
DB_PASSWORD=myser_pass_pwd

BROADCAST_DRIVER="log"
CACHE_DRIVER="file"
FILESYSTEM_DISK="local"
QUEUE_CONNECTION="database"
SESSION_DRIVER="file"
SESSION_LIFETIME="120"

MEMCACHED_HOST="127.0.0.1"

REDIS_HOST="dms-redis-1"
REDIS_PASSWORD=
REDIS_PORT="6379"

MAIL_MAILER="smtp"
MAIL_HOST="mailhog"
MAIL_PORT="1025"
MAIL_USERNAME="null"
MAIL_PASSWORD="null"
MAIL_ENCRYPTION="null"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID="minio"
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION="us-east-1"
AWS_BUCKET="local"
AWS_USE_PATH_STYLE_ENDPOINT="true"
AWS_URL=

PUSHER_APP_KEY="default"
PUSHER_APP_SECRET="default"
PUSHER_APP_ID="default"
PUSHER_APP_HOST="0.0.0.0"
PUSHER_APP_PORT="6001"
PUSHER_APP_CLUSTER="eu"
PUSHER_APP_SCHEME="http"

VITE_PUSHER_APP_KEY="default"
VITE_PUSHER_HOST="0.0.0.0"
VITE_PUSHER_PORT="6001"
VITE_PUSHER_SCHEME="http"
VITE_PUSHER_APP_CLUSTER="eu"

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_RELEASE="v0-6-4-f4face69"
SENTRY_ENVIRONMENT="local"

MEDIA_DISK="s3"

GOOGLE_MAP_API_KEY=

QUERY_DETECTOR_ENABLED="false"
DEBUGBAR_ENABLED="false"

CHECKER_TOKEN=
CHECKER_BASE_URL=
CHECKER_CLIENT_ID=
CHECKER_CLIENT_SECRET=

AI_SERVICE_BASE_URL=
AI_SERVICE_TOKEN=

SCRM_BASE_URL=
SCRM_CLIENT_ID=
SCRM_CLIENT_SECRET=
SCRM_GRANT_TYPE=
SCRM_SCOPE=

ENVELOPE_BASE_URL=
ENVELOPE_APP_ID=
ENVELOPE_AUTH_KEY_ID=
ENVELOPE_AUTH_KEY_SECRET=

MARKETPLACES_ENCRYPTION_KEY=

ELASTICSEARCH_HOST=
ELASTICSEARCH_USER=
ELASTICSEARCH_PASSWORD=

WEBEX24_WEBHOOK_SIGNATURE=

EMAIL=
COMPANY_NAME=
HAS_PWA=

PDF_BASE_URL=

BIG_DATA_BASE_URL=
BIG_DATA_CLIENT_ID=
BIG_DATA_CLIENT_SECRET=
