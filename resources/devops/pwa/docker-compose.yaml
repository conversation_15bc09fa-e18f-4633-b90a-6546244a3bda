x-aliases:
  - &common_parameters
    logging:
      driver: json-file
      options:
        max-file: '5'
        max-size: 20m
    restart: always
    networks:
      - dms_shared_network
    dns:
      - **************
      - **************
      - *******
      - *******

services:
  pwaphpfpm:
    image: ${REGISTRY}/pwa/php:${TAG} #
    hostname: ${VM_NAME}-pwa
    depends_on:
      - postgres
    volumes:
      - /opt/docker/pwa/public:/var/www/html/pwa/public:rw
      - /opt/docker/pwa/storage:/var/www/html/pwa/storage:rw
    environment:
      APP_NAME:
      APP_ENV:
      APP_KEY:
      APP_DEBUG:

      APP_ADMIN_URL:
      APP_ADMIN_CLIENT_ID:
      APP_ADMIN_SECRET:

      BASE_DOMAIN:

      DB_CONNECTION: "pgsql"
      DB_HOST: "postgres"
      DB_PORT: "5432"
      DB_DATABASE:
      DB_USERNAME:
      DB_PASSWORD:

      APP_ID:
      APP_URL:
      APP_SECRET_KEY:
      DMS_HOST:
      DMS_CLIENT_ID:
      DMS_CLIENT_SECRET:
      DMS_PUSHER_APP_KEY:
      DMS_PUSHER_APP_SECRET:

      LOG_CHANNEL:
      LOG_DEPRECATIONS_CHANNEL:
      LOG_LEVEL:

      BROADCAST_DRIVER:

      CACHE_DRIVER:

      FILESYSTEM_DISK:

      QUEUE_CONNECTION:

      SESSION_DRIVER:
      SESSION_LIFETIME:

      MAIL_MAILER:
      MAIL_HOST:
      MAIL_PORT:
      MAIL_USERNAME:
      MAIL_PASSWORD:
      MAIL_ENCRYPTION:
      MAIL_FROM_ADDRESS:
      MAIL_FROM_NAME:

      VITE_APP_NAME:
      VITE_PUSHER_APP_KEY:
      VITE_PUSHER_HOST:
      VITE_PUSHER_PORT:
      VITE_PUSHER_SCHEME:
      VITE_PUSHER_APP_CLUSTER:
      VITE_DMS_HOST:
      VITE_DMS_PUSHER_APP_KEY:

      APP_PUBLIC_STORAGE:

      VAPID_PUBLIC_KEY:
      VAPID_PRIVATE_KEY:

      SENTRY_LARAVEL_DSN:
      SENTRY_TRACES_SAMPLE_RATE:
      SENTRY_PROFILES_SAMPLE_RATE:
      SENTRY_RELEASE:
      SENTRY_ENVIRONMENT:
      VITE_SENTRY_DSN_PUBLIC: "${SENTRY_LARAVEL_DSN}"
    <<: *common_parameters

  postgres:
    image: ${REGISTRY}/pwa/postgres:${TAG}
    environment:
      POSTGRES_USER: $DB_USERNAME
      POSTGRES_PASSWORD: $DB_PASSWORD
      POSTGRES_DB: $DB_DATABASE
    ports:
      - "5432:5432"
    volumes:
      - database:/var/lib/postgresql/data
    <<: *common_parameters

volumes:
  database:

networks:
  dms_shared_network:
    external: true
