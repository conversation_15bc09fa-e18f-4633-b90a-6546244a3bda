COMPOSE_PROJECT_NAME=pwa

REGISTRY=registry.support.dilizy.dev
TAG=dev-docker-f3da1e9e
VM_NAME=dilizy-dev-pwa

PWA_HOSTNAME=pwa.dilizy.dev


#################### PWA ENV ########################
APP_ENV=
APP_KEY=
APP_DEBUG=

APP_ADMIN_URL=
APP_ADMIN_CLIENT_ID=
APP_ADMIN_SECRET=

BASE_DOMAIN=

APP_ID=
APP_NAME=
APP_URL=
APP_SECRET_KEY=

DB_CONNECTION="pgsql"
DB_HOST="postgres"
DB_PORT="5432"
DB_DATABASE="pwa_db"
DB_USERNAME="postgres"
DB_PASSWORD="pwa_db_pass"

DMS_HOST=
DMS_CLIENT_ID=
DMS_CLIENT_SECRET=
DMS_PUSHER_APP_KEY=
DMS_PUSHER_APP_SECRET=

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

BROADCAST_DRIVER=pusher

CACHE_DRIVER=file

FILESYSTEM_DISK=common

QUEUE_CONNECTION=database

SESSION_DRIVER=database
SESSION_LIFETIME=217000

MAIL_MAILER=smtp
MAIL_HOST="sandbox.smtp.mailtrap.io"
MAIL_PORT="587"
MAIL_USERNAME="null"
MAIL_PASSWORD="null"
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

VITE_DMS_HOST="${DMS_HOST}"
VITE_DMS_PUSHER_APP_KEY="${DMS_PUSHER_APP_KEY}"

SENTRY_LARAVEL_DSN=
# Specify a fixed sample rate
SENTRY_TRACES_SAMPLE_RATE=1.0
# Set a sampling rate for profiling - this is relative to traces_sample_rate
SENTRY_PROFILES_SAMPLE_RATE=1.0
SENTRY_RELEASE=dev-docker-f3da1e9e
SENTRY_ENVIRONMENT="local"
VITE_SENTRY_DSN_PUBLIC=

VAPID_PUBLIC_KEY=
VAPID_PRIVATE_KEY=
