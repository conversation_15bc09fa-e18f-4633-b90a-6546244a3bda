export default function StatusTag({ status }) {
    function mapColorClass(status) {
        switch (status) {
            case 'pending':
                return 'is-info'
            case 'running':
                return 'is-warning'
            case 'finished':
                return 'is-success'
            case 'failed':
                return 'is-danger'
            case 'canceled':
                return 'is-black'
        }

        return 'is-white'
    }

    const colorClass = mapColorClass(status)

    return (
        <span className={`tag ${colorClass}`}>{status}</span>
    )
}
