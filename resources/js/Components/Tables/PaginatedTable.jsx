import Loader from './../../Components/Loader'
import { useEffect, useState } from 'react'
import axios from "axios"

export default function PaginatedTable({ headers, url }) {
    const columns = headers.map(({ title }) => {
        return (<th>{title}</th>)
    })

    useEffect(() => {
        fetchData(url)
    }, [])

    // Table data state.
    const [isLoading, setIsLoading] = useState(true)
    const [response, setResponse] = useState(null)

    // Pagination data and controls.
    const [page, setPage] = useState(1)
    const [previousBtn, setPreviousBtn] = useState({disabled: true, url: null})
    const [nextBtn, setNextBtn] = useState({disabled: true, url: null})

    const rows = response?.data.map((row) => {
        const tds = headers.map(({ value }) => {
            return (<td>{value(row)}</td>)
        })

        return (<tr>{tds}</tr>)
    })

    function updateData(response) {
        setResponse(response)
    }

    function fetchData(url) {
        setIsLoading(true)

        axios
            .get(url)
            .then((response) => setResponse(response.data))
            .finally(() => setIsLoading(false))
    }

    return (
        <div>
            <Loader isLoading={isLoading}>
                <table class="table">
                    <thead>
                        <tr>
                            {columns}
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            {columns}
                        </tr>
                    </tfoot>
                    <tbody>
                        { response && (
                            rows
                        ) }
                    </tbody>
                </table>
            </Loader>
            <nav class="pagination is-right" role="navigation" aria-label="pagination">
                <a
                    onClick={() => !previousBtn.disabled && fetchData(previousBtn.url)}
                    className={`pagination-previous ${previousBtn.disabled ? 'is-disabled' : ''}`}
                >
                    Previous
                </a>
                <a
                    onClick={() => !nextBtn.disabled && fetchData(nextBtn.url)}
                    className={`pagination-next ${nextBtn.disabled ? 'is-disabled' : ''}`}
                >
                    Next page
                </a>
            </nav>
        </div>
    )
}
