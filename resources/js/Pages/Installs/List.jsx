import Layout from './../Layout'
import PaginatedTable from './../../Components/Tables/PaginatedTable'
import TypeTag from './../../Components/Elements/Installs/TypeTag'
import StatusTag from './../../Components/Elements/Installs/StatusTag'
import { Head, Link } from '@inertiajs/react'

export default function List({ user }) {
    function Actions({ row }) {
        return (
            <Link class="button is-small" href={`/admin/installs/${row.id}`}>View</Link>
        )
    }

    const headers = [
        { title: 'Id', value: (row) => row.id },
        { title: 'Type', value: (row) => (<TypeTag type={row.type} />) },
        { title: 'Payload', value: (row) => (<code>{JSON.stringify(row.payload)}</code>) },
        { title: 'Status', value: (row) => (<StatusTag status={row.status} />) },
        { title: 'Actions', value: (row) => (<Actions row={row} />) },
    ]

    return (
        <Layout>
            <Head title="Installs > List all" />
            <PaginatedTable
                headers={headers}
                url="/admin/installs/api/list"
            />
        </Layout>
    )
}
