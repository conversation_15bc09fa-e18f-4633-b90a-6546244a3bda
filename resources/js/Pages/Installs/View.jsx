import Layout from './../Layout'
import { Head } from '@inertiajs/react'
import TypeTag from './../../Components/Elements/Installs/TypeTag'
import StatusTag from './../../Components/Elements/Installs/StatusTag'
import Stage from './Partials/Stage'

export default function View({ user, install }) {
    const title = `Installs > View > #${install.id}`

    return (
        <Layout>
            <Head title={title} />
            <p class="title">{title}</p>

            <div class="block">
                <div class="field is-grouped is-grouped-multiline">
                    <TypeTag type={install.type} />
                    <StatusTag status={install.status} />
                </div>
            </div>

            <div class="block">
                <p>Payload:</p>
                <pre style={{'white-space': 'pre-wrap'}}>
                    {JSON.stringify(install.payload, undefined, 2)}
                </pre>
            </div>

            <div class="block">
                <Stage stage={{}} />
            </div>
        </Layout>
    )
}
