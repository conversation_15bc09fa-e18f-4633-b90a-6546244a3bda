import { useState } from 'react'

export default function Stage({ stage }) {
    const [isLogVisible, setIsLogVisible] = useState(false)

    function toggleLog() {
        setIsLogVisible(!isLogVisible)
    }

    function mapColorClass(status) {
        switch (status) {
            case 'pending':
                return 'is-info'
            case 'running':
                return 'is-warning'
            case 'finished':
                return 'is-success'
            case 'failed':
                return 'is-danger'
            case 'canceled':
                return 'is-dark'
        }

        return ''
    }

    const colorClass = mapColorClass(stage.status)

    return (
        <article className={`message ${colorClass}`}>
            <div class="message-header">
                <p>Success</p>
                <button class="button is-small" onClick={toggleLog}>{ isLogVisible ? 'Hide' : 'Show' }</button>
            </div>
            { isLogVisible && (
                <div class="message-body">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec
                    nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus
                    diam, et dictum <a>felis venenatis</a> efficitur. A<PERSON>an ac
                    <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et
                    sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a
                    neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
                </div>
            ) }
        </article>
    )
}
