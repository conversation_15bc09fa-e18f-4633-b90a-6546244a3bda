import { Link } from '@inertiajs/react'
import { usePage } from '@inertiajs/react'
import { useState } from 'react'

export default function Navbar({ children }) {
    const { url, component } = usePage()
    const [isMenuVisible, setIsMenuVisible] = useState(false)

    function toggleMenu() {
        setIsMenuVisible(!isMenuVisible)
    }

    return (
        <div>
            <nav class="navbar is-black" role="navigation" aria-label="main navigation">
                <div class="navbar-brand">
                    <span class="navbar-item">Deployer Admin</span>
                    <a class="navbar-burger" role="button" aria-label="menu" aria-expanded="false" onClick={toggleMenu}>
                        <span aria-hidden="true"></span>
                        <span aria-hidden="true"></span>
                        <span aria-hidden="true"></span>
                        <span aria-hidden="true"></span>
                    </a>
                </div>
                <div className={`navbar-menu ${isMenuVisible ? 'is-active' : ''}`}>
                    <div class="navbar-start">
                        <Link class="navbar-item" href="/admin/main">Main</Link>
                        <div class="navbar-item has-dropdown is-hoverable">
                            <a class="navbar-link">Versions</a>
                            <div class="navbar-dropdown">
                                <Link class="navbar-item" href="/admin/versions/list">List all</Link>
                            </div>
                        </div>

                        <div class="navbar-item has-dropdown is-hoverable">
                            <a class="navbar-link">Installs</a>
                            <div class="navbar-dropdown">
                                <Link class="navbar-item" href="/admin/installs/list">List all</Link>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="container">{children}</div>
        </div>
    )
}
